/*
 * CO_eeprom.c
 *
 *  Created on: Apr 1, 2025
 *      Author: alexander
 */

#include <CO_eeprom.h>
#include "eeprom_STM32.h"
#include "CO_eepromStorageModeleT.h"
#include <stdlib.h>
#include "crc16-ccitt.h"
extern uint16_t VirtAddVarTab[NB_OF_VAR];


static bool_t isModuleInit = false;
bool_t CO_eeprom_init(void *storageModule) {

	//if (isModuleInit)
	//	return true;
	storageModyle_t *modulePtr = (storageModyle_t *)storageModule;


	modulePtr->freeMemLeft = NB_OF_VAR ;
	modulePtr->nextFreeAdr = 0;

	for (int i = 0; i < NB_OF_VAR; i++) {
		VirtAddVarTab[i] = i;
	}
	HAL_FLASH_Unlock();
	if (EE_Init() != HAL_OK) {
		return false;
	}
	isModuleInit = true;
	return true;
}

size_t CO_eeprom_getAddr(void *storageModule, bool_t isAuto, size_t len,
		bool_t *overflow) {
	storageModyle_t *modulePtr=(storageModyle_t *)storageModule;
	size_t ret=modulePtr->nextFreeAdr;
	if (modulePtr->freeMemLeft>=len)
	{
		modulePtr->freeMemLeft-=len;
		modulePtr->nextFreeAdr+=len;
		*overflow=false;
	}
	else {
		*overflow=true;
	}
	return ret;
}

uint16_t __readByte(uint16_t adr, uint8_t* Data)
{
	uint16_t readData=0;
	uint16_t ret;
	ret = EE_ReadVariable(adr, &readData);
	*Data=(uint8_t)(readData&0xff);
	return ret;
}

uint16_t __writeByte(uint16_t adr, uint8_t Data)
{
	uint16_t ret;
	uint16_t writeData=(uint16_t)Data;
	ret = EE_WriteVariable(adr, writeData);
	return ret;
}


bool_t CO_eeprom_updateByte(void *storageModule, uint8_t data, size_t eepromAddr)
{
	uint16_t ret=0;
	uint8_t tmpDat;
	bool ret2=true;
	ret = __readByte((uint16_t)eepromAddr,&tmpDat);
	if (ret==0)
	{
		if (data!=tmpDat)
		{
			ret=__writeByte(eepromAddr,data);
			if (ret!=0)
			{
				ret2=false;
			}
		}
	}
	else {
		ret2=false;
	}
	return ret2;
}

void CO_eeprom_readBlock(void *storageModule, uint8_t *data, size_t eepromAddr, size_t len)
{
	uint8_t *dataPtr=data;
	for (uint16_t i = eepromAddr; i<(eepromAddr+len); i++)
	{
		__readByte(i,dataPtr);
		dataPtr++;
	}

}

bool_t CO_eeprom_writeBlock(void *storageModule, uint8_t *data, size_t eepromAddr, size_t len)
{
	uint8_t *dataPtr=data;
	int eeRet=0;
	bool CORet=true;
	for (uint16_t i = eepromAddr; i<(eepromAddr+len); i++)
	{
		eeRet=__writeByte(i,*dataPtr);

		if (eeRet!=0)
		{
			eeRet=__writeByte(i,*dataPtr);
		}
		dataPtr++;
		if (eeRet!=0)
		{
			CORet=false;
			return CORet;
		}
	}

	return CORet;
}

uint16_t CO_eeprom_getCrcBlock(void *storageModule, size_t eepromAddr, size_t len)
{

	uint16_t crcRet=0;
	uint8_t readData;
	for (uint16_t i = eepromAddr; i<(eepromAddr+len); i++)
	{
		__readByte(i,&readData);
		crc16_ccitt_single(&crcRet, readData);
	}
	return crcRet;
}
