#ifndef INC_COLOR_PALETE_H_
#define INC_COLOR_PALETE_H_

#include "tim.h"

/**
 * @addtogroup TimerControl
 * @{
 */

/**
 * @brief Timer object
 */
typedef struct
{
	TIM_HandleTypeDef *htim;		///< Pointer to the HAL Timer handle
	uint32_t Counter;				///< Callback counter
	void (*TimerCallback)(void*);	///< Callback function pointer
	void *CallbackArg;				///< Argument to be passed to the callback function
} Timer_obj;

void Timer_Init(Timer_obj *this, TIM_HandleTypeDef *htim); ///< Initialize a timer object
void Timer_Start(Timer_obj *this);						   ///< Start the timer
void Timer_Start_IT(Timer_obj *this);					   ///< Start the timer in interrupt mode
uint32_t Timer_Get(Timer_obj *this);					   ///< Get the current value of the timer
uint32_t Timer_GetCounter(Timer_obj *this);				   ///< Get the current value of the callback counter
void Timer_Set(Timer_obj *this, uint32_t value);		   ///< Set the timer value
void Timer_SetCounter(Timer_obj *this, uint32_t value);    ///< Set the timer callback counter
void Timer_Reset(Timer_obj *this);						   ///< Reset the timer
void Timer_ResetCounter(Timer_obj *this);				   ///< Reset the timer callback counter

/**
 * @brief   Handle interrupt for timer control
 * @details This function updates the callback counter and calls Callback function
 * @note    This function must be called in HAL_TIM_PeriodElapsedCallback
 */
void TimerControl_HandleInterrupt(TIM_HandleTypeDef *htim);

/**
 * @brief Registers a timer object when it is initialized, for working with it in interrupts
 * @note  In the .c file sets the maximum number of registered timers
 */
void _RegisterTimer(Timer_obj *timer);

/** @} */

#endif
