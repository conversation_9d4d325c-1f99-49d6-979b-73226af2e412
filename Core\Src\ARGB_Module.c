/**
 * @file 	ARGB_Module.c
 * <AUTHOR>
 * @date 	30-November-2023
 * @brief 	Module source file for the ARGB library by <PERSON> <PERSON>ks
 * @note	This module adds additional functions for the ARGB library
 */

#include "ARGB_Module.h"

#undef DELAY
    #ifdef USE_OS_DELAY
        #define DELAY osDelay
    #elif defined(USE_NOTIFY_DELAY)
        #define DELAY(x) DelayWithNotification(pdMS_TO_TICKS(x), xTaskGetCurrentTaskHandle())
    #elif defined(USE_NOTIFY_DELAY_BREAK)
        #define NOTIFY_DELAY(x) DelayWithNotification(pdMS_TO_TICKS(x), xTaskGetCurrentTaskHandle())
        #define DELAY(x) if (NOTIFY_DELAY(x)) break;
    #else
        #define DELAY HAL_Delay
#endif

void ARGB_SetRGB_Inverted(u16_t i, u8_t r, u8_t g, u8_t b)
{
    u16_t inverted_index = NUM_PIXELS - 1 - i;
    ARGB_SetRGB(inverted_index, r, g, b);
}

void ARGB_FillRGB_Betwen(u16_t start, u16_t end, u8_t r, u8_t g, u8_t b)
{
	for (u16_t i = start; i<= end; i++)
	{
		ARGB_SetRGB(i, r, g, b);
	}
}

void ARGB_FillThreeStates(uint8_t State, uint16_t startLen, uint16_t end_len)
{
	if(State == 0) // No
	{
		ARGB_FillRGB_Betwen(startLen, end_len, 255,255,255);
	}
	if(State == 1) // Yes
	{
		ARGB_FillRGB_Betwen(startLen, end_len, 255,255,0);
	}
	if(State == 2) // Extreme
	{
		ARGB_FillRGB_Betwen(startLen, end_len, 255,0,0);
	}
	while (ARGB_Show() != ARGB_OK);
}

void ARGB_RunningLine_BLOCKING(int start, int end, u8_t r, u8_t g, u8_t b, int delay_ms)
{
	ARGB_FillRGB(r, g, b);
	for (int i = start; i <= end; i++)
	{
		ARGB_SetRGB(i, r, g, b);
		while (ARGB_Show() != ARGB_OK);

		DELAY(delay_ms);

		ARGB_SetRGB(i, 0, 0, 0);
	}
}

void ARGB_Blink_BLOCKING(int start, int end, u8_t r, u8_t g, u8_t b, int delay_ms, int BlinkCount)
{
	for (int i = 0; i <= BlinkCount; i++)
	{
		ARGB_Clear();
		while (ARGB_Show() != ARGB_OK);
		DELAY(delay_ms);
		ARGB_FillRGB_Betwen(start, end, r, g, b);
		while (ARGB_Show() != ARGB_OK);
		DELAY(delay_ms);
	}
}

void ARGB_Fade_BLOCKING(u8_t start, u8_t end, u8_t r, u8_t g, u8_t b, u8_t delay_ms, u8_t currentBrightness)
{
	int brightness = currentBrightness;
	for(; brightness >= 0; brightness--)
	{
		ARGB_SetBrightness(brightness);
		ARGB_FillRGB_Betwen(start, end, r, g, b);
		while (ARGB_Show() != ARGB_OK);
		DELAY(delay_ms);
	}brightness++;
	for(; brightness <= currentBrightness; brightness++)
	{
		ARGB_SetBrightness(brightness);
		ARGB_FillRGB_Betwen(start, end, r, g, b);
		while (ARGB_Show() != ARGB_OK);
		DELAY(delay_ms);
	}
}

void ARGB_Blink(argbModule_obj *anim, uint32_t start, uint32_t end, u8_t r, u8_t g, u8_t b, uint32_t delay_ms, uint8_t BlinkCount, uint32_t post_delay_ms)
{
	AnimationState_t *state = &anim->state[0];

	if (!anim->is_running)
	{
		// Multiply by 2, because blinking involves two steps: on and off.
		Animation_Init(anim, delay_ms, BlinkCount * 2, 0);
		Animation_Init(anim, post_delay_ms, 1, 1);
	}

	if(Animation_Update(anim, 0))
	{
		if(!(state->current_step % 2 == 0))
		{
			ARGB_FillRGB_Betwen(start, end, r, g, b);
		}
		else
		{
			ARGB_Clear();
		}
		while (ARGB_Show() != ARGB_OK);
	}

	Animation_Update(anim, 1);
}

void ARGB_Fade(argbModule_obj *anim, uint32_t start, uint32_t end, u8_t r, u8_t g, u8_t b, uint32_t delay_ms, u8_t currentBrightness)
{
	AnimationState_t *state = &anim->state[0];
	uint8_t setBrightness;

	if (!anim->is_running)
	{
		Animation_Init(anim, delay_ms, currentBrightness*2, 0);
	}

	if (Animation_Update(anim, 0))
	{
		if(state->current_step <= currentBrightness)
		{
			setBrightness = state->current_step;
			ARGB_SetBrightness(setBrightness);
			ARGB_FillRGB_Betwen(start, end, r, g, b);
			while (ARGB_Show() != ARGB_OK);
		}

		else if(state->current_step >= currentBrightness)
		{
			uint8_t x = state->current_step - currentBrightness;
			setBrightness = currentBrightness - (x);
			ARGB_SetBrightness(setBrightness);
			ARGB_FillRGB_Betwen(start, end, r, g, b);
			while (ARGB_Show() != ARGB_OK);
		}

		ARGB_SetBrightness(currentBrightness);

	}
}

void ARGB_RunningLine(argbModule_obj *anim, int start, int end, u8_t r, u8_t g, u8_t b,
					 int delay_ms, RuningLine_t RuningLineType, uint8_t SizieOfLine, uint32_t post_delay_ms, Direction_t direction)
{
	AnimationState_t *state = &anim->state[0];

	uint32_t cordColor;
	int cordResetColor;

	if(RuningLineType == RYNNINGLINE_CYCLED)
	{
	    if(!anim->is_running)
	    {
	        Animation_Init(anim, delay_ms, end, 0);
	    }

	    if(Animation_Update(anim, 0))
	    {
	        cordColor = state->current_step-1;
	        cordResetColor = cordColor-SizieOfLine;

	        if(cordColor >= 0 && cordColor <= SizieOfLine)
	        {
	        	uint8_t lastEreaseCord = end-SizieOfLine;
	        	cordResetColor = lastEreaseCord + cordColor;
	        }

	        if (direction == DIRECTION_NORMAL)
	        {
		        ARGB_SetRGB(cordColor, r, g, b);
		        ARGB_SetRGB(cordResetColor, 0, 0, 0);
	        }
	        else if (direction == DIRECTION_INVERTED)
	        {
		        ARGB_SetRGB_Inverted(cordColor-1, r, g, b);
		        ARGB_SetRGB_Inverted(cordResetColor-1, 0, 0, 0);
	        }

	        while (ARGB_Show() != ARGB_OK);
	    }
	}

	if(RuningLineType == RYNNINGLINE_NORMAL)
	{
	    if(!anim->is_running)
	    {
	    	Animation_Init(anim, delay_ms, end+SizieOfLine, 0);
	    	Animation_Init(anim, post_delay_ms, 1, 1);
	    }

	    if(Animation_Update(anim, 0))
	    {
	        cordColor = state->current_step-1;
	        cordResetColor = cordColor-SizieOfLine;
	        cordColor = (state->current_step >= end) ? end-1 : cordColor;

	        if (direction == DIRECTION_NORMAL)
	        {
		        ARGB_SetRGB(cordColor, r, g, b);
		        ARGB_SetRGB(cordResetColor, 0, 0, 0);
	        }
	        else if (direction == DIRECTION_INVERTED)
	        {
		        ARGB_SetRGB_Inverted(cordColor, r, g, b);
		        ARGB_SetRGB_Inverted(cordResetColor, 0, 0, 0);
	        }

	        while (ARGB_Show() != ARGB_OK);
	    }

	    if(Animation_Update(anim, 1))
	    {
	    	ARGB_Clear();
	    	while (ARGB_Show() != ARGB_OK);
	    }
	}

	if(RuningLineType == RYNNINGLINE_DONTERASE)
	{
	    if(!anim->is_running)
	    {
	    	Animation_Init(anim, delay_ms, end, 0);
	    	Animation_Init(anim, 1000, 1, 1);
	    	ARGB_Clear();
	    	while (ARGB_Show() != ARGB_OK);
	    }

	    if(Animation_Update(anim, 0))
	    {
	        cordColor = state->current_step-1;

	        if (direction == DIRECTION_NORMAL)
	        {
		        ARGB_SetRGB(cordColor, r, g, b);
	        }
	        else if (direction == DIRECTION_INVERTED)
	        {
		        ARGB_SetRGB_Inverted(cordColor, r, g, b);
	        }

	        if(state->current_step == end+1)
	        {
	        	ARGB_Clear();
	        }

	        while (ARGB_Show() != ARGB_OK);
	    }

	    Animation_Update(anim, 1);
	}

	if(RuningLineType == RYNNINGLINE_DONTERASELASTPIXELS)
	{
	    if(!anim->is_running)
	    {
	    	Animation_Init(anim, delay_ms, end, 0);
	    	Animation_Init(anim, post_delay_ms, 1, 1);
	    }

	    if(Animation_Update(anim, 0))
	    {
	        cordColor = state->current_step-1;
	        cordResetColor = cordColor-SizieOfLine;
	        cordColor = (state->current_step >= end) ? end-1 : cordColor;

	        if (direction == DIRECTION_NORMAL)
	        {
		        ARGB_SetRGB(cordColor, r, g, b);
		        ARGB_SetRGB(cordResetColor, 0, 0, 0);
	        }
	        else if (direction == DIRECTION_INVERTED)
	        {
		        ARGB_SetRGB_Inverted(cordColor, r, g, b);
		        ARGB_SetRGB_Inverted(cordResetColor, 0, 0, 0);
	        }

	        while (ARGB_Show() != ARGB_OK);
	    }

	    if(Animation_Update(anim, 1))
	    {
	    	ARGB_Clear();
	    	while (ARGB_Show() != ARGB_OK);
	    }
	}

}

void ARGB_RainbowColor(argbModule_obj *anim, uint32_t startPalete, uint32_t endPalete, RainbowColor_t direction, uint32_t range, uint32_t delay_ms)
{
    uint32_t current_step = anim->state[0].current_step;
    uint32_t sizee = range;

    if (!anim->is_running)
    {
        Animation_Init(anim, delay_ms, ((endPalete - startPalete) / sizee) * 2, 0);
    }

    if (Animation_Update(anim, 0))
    {
        uint16_t hue1 = startPalete;
        uint16_t hue2 = startPalete;
        uint8_t hue1ChangeDir = false;
        uint8_t hue2ChangeDir = false;

        if(direction == UP)
        {
        	int hue1Cord = current_step;
        	int hue2Cord = current_step+1;

            for (int i = hue1Cord; i <= NUMB_PIXELS; i++)
            {
            	(hue1 >= 0 || hue1 <= NUMB_PIXELS) ? ARGB_SetHSV(i, hue1, 255, 255) : 0;
            	hue1 = (hue1ChangeDir) ? hue1 - sizee : hue1 + sizee;
            	if((hue1 >= endPalete) || (hue1 <= startPalete))
            	{
            		hue1ChangeDir = (hue1ChangeDir) ? false : true;
            	}
            }

            for (int i = hue2Cord; i >= 0; i--)
            {
            	(hue2 >= 0 || hue2 <= NUMB_PIXELS) ? ARGB_SetHSV(i, hue2, 255, 255) : 0;
            	hue2 = (hue2ChangeDir) ? hue2 - sizee : hue2 + sizee;
            	if((hue2 >= endPalete) || (hue2 <= startPalete))
            	{
            		hue2ChangeDir = (hue2ChangeDir) ? false : true;
            	}
            }
        }

        if(direction == DOWN)
        {
            int hue1Cord = endPalete-current_step;
            int hue2Cord = endPalete-current_step+1;

            for (int i = hue1Cord; i >= 0; i--)
            {
            	(hue1 >= 0 || hue1 <= SIZE_MAX) ? ARGB_SetHSV(i, hue1, 255, 255) : 0;
            	hue1 = (hue1ChangeDir) ? hue1 - sizee : hue1 + sizee;
            	if((hue1 >= endPalete) || (hue1 <= startPalete))
            	{
            		hue1ChangeDir = (hue1ChangeDir) ? false : true;
            	}
            }

            for (int i = hue2Cord; i <= NUMB_PIXELS; i++)
            {
            	(hue2 >= 0 || hue2 <= NUMB_PIXELS) ? ARGB_SetHSV(i, hue2, 255, 255) : 0;
            	hue2 = (hue2ChangeDir) ? hue2 - sizee : hue2 + sizee;
            	if((hue2 >= endPalete) || (hue2 <= startPalete))
            	{
            		hue2ChangeDir = (hue2ChangeDir) ? false : true;
            	}
            }
        }

        while (ARGB_Show() != ARGB_OK);
    }
}

void ARGB_SetTime(argbModule_obj *anim, uint32_t value)
{
	anim->time = value;
}

void ARGB_EndAnimation(argbModule_obj *argbModule)
{
	_Animation_End(argbModule);
}

void Animation_Init(argbModule_obj *anim, uint32_t delay, uint32_t end_step, uint32_t animState)
{
	AnimationState_t *state = &anim->state[animState];

    anim->stateCount++;
    anim->stateCurrent = 1;
    state->delay = delay;
    state->end_step = end_step;
    state->start_time = (anim->statePrDelay * anim->statePrEndStep) + delay;
    anim->stateTime[anim->stateCount-1] = (anim->stateCount > 1) ?
    (delay * end_step) + anim->stateTime[anim->stateCount-2] : (delay * end_step);
    anim->statePrDelay = delay;
    anim->statePrEndStep = end_step;
    anim->is_running = true;
}

bool Animation_Update(argbModule_obj *anim, uint32_t animState)
{

	AnimationState_t *state = &anim->state[animState];
	uint32_t elapsed_time = anim->time - state->start_time;
	uint32_t new_step = ( (elapsed_time / state->delay) % (state->end_step) ) + 1;

	if(anim->time < state->start_time) return false;

	static uint8_t endAnimationStateFl = false;
    static uint8_t endAnimationFl = false;

    if(anim->time > anim->stateTime[anim->stateCurrent-1] && anim->stateTime[anim->stateCurrent] != 0)
    {
    	anim->stateCurrent++;
    }

    if (anim->stateCurrent-1 == animState)
    {
        if (anim->is_running)
        {
            if (new_step != state->current_step)
            {
                state->current_step = new_step;
                endAnimationStateFl = (state->current_step == state->end_step);
                endAnimationFl = (endAnimationStateFl && anim->stateCurrent == anim->stateCount);
                return (state->current_step == 0) ? false : true;
            }
            else
            {
            	if (endAnimationStateFl)
            	{
                	state->current_step = 0;
                	endAnimationStateFl = false;
            	}
            	if(endAnimationFl)
            	{
            		_Animation_End(anim);
            		endAnimationFl = false;
            	}
            	return false;
            }
        }
    }
    return false;
}

#include "TimerControl.h"
#include "main.h"
extern Timer_obj Timer;
void _Animation_End(argbModule_obj *anim)
{
	Timer_ResetCounter(&Timer);
	anim->is_running = false;
	anim->stateCurrent = 0;
	anim->stateCount = 0;
	anim->is_running = 0;
	anim->statePrDelay = 0;
	anim->statePrEndStep = 0;
    for (int i = 0; i<= STATE_MAX; i++)
    {
    	anim->stateTime[i] = 0;
    }
}

#if defined(USE_FREERTOS)
uint32_t DelayWithNotification(uint32_t delayTicks, TaskHandle_t notifyingTask) {
    uint32_t ulNotificationValue;
    xTaskNotifyWait(0x00,          // Do not clear notification bits on entry.
                    0xFFFFFFFF,    // Clear all notification bits on exit.
                    &ulNotificationValue, // The notification value is passed here.
                    delayTicks);   // Delay in ticks

    return ulNotificationValue;
}
#endif
