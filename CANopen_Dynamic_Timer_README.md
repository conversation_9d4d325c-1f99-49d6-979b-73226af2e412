# CANopen Dynamic Timer Implementation

## Обзор

Данная реализация добавляет динамическое управление таймером для CANopen PDO синхронизации, используя параметры `timeDifference_us` и `timerNext_us` для оптимизации производительности и распределения нагрузки между устройствами.

## Основные изменения

### 1. Модифицированные файлы

- `Libs\CanOpenSTM32\CANopenNode_STM32\CO_app_STM32.c` - Основная реализация
- `Libs\CanOpenSTM32\CANopenNode_STM32\CO_app_STM32.h` - Новые функции API
- `Core\Src\main.c` - Обработчик прерывания таймера
- `Core\Src\proc_CANopen.c` - Модифицированная задача RealTimeProc

### 2. Новые функции

#### `getCurrentTime_us()`
- Использует DWT (Data Watchpoint and Trace) для высокоточного измерения времени
- Обеспечивает микросекундную точность на Cortex-M3/M4
- Автоматически инициализирует DWT при первом вызове

#### `setTimerPeriod_us(uint32_t period_us)`
- Динамически настраивает период таймера на основе `timerNext_us`
- Учитывает частоту таймера и предделители
- Ограничивает период разумными границами (10 тиков - 65535 тиков)

#### `canopen_app_interrupt()`
- Измеряет реальное время между вызовами
- Передает `timeDifference_us` в CANopen функции
- Получает `timerNext_us` от CANopen функций
- Настраивает таймер на следующее событие

### 3. Механизм работы

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Timer IRQ     │───▶│   Semaphore      │───▶│  RealTimeProc   │
│   Handler       │    │   CO_Timer_      │    │   Task          │
└─────────────────┘    │   Semaphore      │    └─────────────────┘
                       └──────────────────┘             │
                                                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  setTimerPeriod  │◀───│ canopen_app_    │
                       │  _us()           │    │ interrupt()     │
                       └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  timerNext_us    │◀───│ CO_process_*()  │
                       │  (output)        │    │ functions       │
                       └──────────────────┘    └─────────────────┘
                                                        ▲
                       ┌──────────────────┐             │
                       │ timeDifference_us│─────────────┘
                       │ (input)          │
                       └──────────────────┘
```

## Преимущества

### 1. Оптимизация производительности
- Таймер срабатывает только когда нужно
- Снижение нагрузки на процессор
- Более эффективное использование ресурсов

### 2. Точная синхронизация
- Микросекундная точность измерения времени
- Реальное время между вызовами функций
- Адаптивная настройка периода таймера

### 3. Распределение нагрузки
- CANopen функции сами определяют время следующего события
- Автоматическое распределение событий во времени
- Предотвращение коллизий в сети CAN

## Использование

### Инициализация
```c
// Инициализация происходит автоматически в canopen_app_init()
// Семафор CO_Timer_Semaphore создается автоматически
```

### Получение статистики
```c
CO_TimingStats_t stats;
canopen_app_getTimingStats(&stats);
printf("Next event in: %lu us\n", stats.nextTimerEvent_us);
```

### Принудительное выполнение
```c
canopen_app_forceInterrupt(); // Принудительный вызов обработки
```

## Тестирование

Включены тестовые функции для проверки работы:

```c
#include "canopen_timer_test.h"

// В main() или другой задаче
canopen_timer_test_init();

// Периодически вызывать
canopen_timer_test_run();
```

## Конфигурация

### Ограничения таймера
```c
#define MIN_TIMER_PERIOD_US  100     // Минимум 100 мкс
#define MAX_TIMER_PERIOD_US  100000  // Максимум 100 мс
#define DEFAULT_PERIOD_US    1000    // По умолчанию 1 мс
```

### Требования к оборудованию
- Cortex-M3/M4 процессор с DWT
- Таймер с прерываниями
- FreeRTOS для семафоров

## Отладка

### Проверка работы DWT
```c
if (!(CoreDebug->DEMCR & CoreDebug_DEMCR_TRCENA_Msk)) {
    // DWT не инициализирован
}
```

### Мониторинг событий таймера
```c
uint32_t next_event = canopen_app_getNextTimerEvent_us();
// Логирование изменений next_event
```

## Возможные проблемы

1. **DWT не работает** - Проверить поддержку в отладчике
2. **Переполнение таймера** - Ограничения на 16-битные таймеры
3. **Семафор не создан** - Проверить инициализацию FreeRTOS

## Дальнейшие улучшения

1. Добавить поддержку 32-битных таймеров
2. Реализовать fallback для систем без DWT
3. Добавить статистику производительности
4. Оптимизировать для низкого энергопотребления
