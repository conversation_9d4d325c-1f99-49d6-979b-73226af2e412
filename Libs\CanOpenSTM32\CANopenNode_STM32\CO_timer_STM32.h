/*
 * CANopen Dynamic Timer Management for STM32
 *
 * @file        CO_timer_STM32.h
 * <AUTHOR> Assistant
 * @date        2024
 *
 * High-precision timer management for CANopen PDO synchronization with automatic
 * prescaler calculation and support for 1 microsecond to 60 second range.
 */

#ifndef CO_TIMER_STM32_H
#define CO_TIMER_STM32_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"
#include "CANopen.h"

/* Timer statistics structure */
typedef struct
{
    uint32_t lastTimeDifference_us;     /* Last time difference in microseconds */
    uint32_t nextTimerEvent_us;         /* Next timer event in microseconds */
    uint32_t currentTime_us;            /* Current time in microseconds */
    bool_t timerInitialized;            /* Timer initialization flag */
} CO_TimingStats_t;

/* Timer range structure */
typedef struct
{
    uint32_t min_period_us;             /* Minimum period in microseconds */
    uint32_t max_period_us;             /* Maximum period in microseconds */
    uint32_t resolution_us;             /* Best resolution in microseconds */
    uint32_t timer_clock_hz;            /* Timer clock frequency in Hz */
} CO_timer_range_t;

/* Get current time in microseconds */
uint32_t getCurrentTime_us(void);

/* Set timer period with automatic prescaler calculation */
void setTimerPeriod_us(uint32_t period_us);

/* Initialize timer management module */
void CO_timer_init_STM32(void);

/* Process CANopen interrupt with dynamic timing */
void CO_timer_processInterrupt(bool_t *syncWas, uint32_t *timeDifference_us, uint32_t *timerNext_us);

/* Get next scheduled timer event time */
uint32_t CO_timer_getNextTimerEvent_us(void);

/* Process next timer event after CANopen functions */
void CO_timer_processNextEvent(uint32_t timerNext_us_from_canopen);

/* Get timer period range and resolution */
void CO_timer_getRange(CO_timer_range_t *range);

/* Get timing statistics for debugging */
void CO_timer_getTimingStats(CO_TimingStats_t *stats);

#ifdef __cplusplus
}
#endif

#endif /* CO_TIMER_STM32_H */
