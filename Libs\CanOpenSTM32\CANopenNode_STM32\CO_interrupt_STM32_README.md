# CANopen Interrupt and Timer Management Module for STM32

## Overview

Module `CO_interrupt_STM32` provides high-precision timer management and FreeRTOS synchronization for CANopen PDO with automatic prescaler calculation and support for 1 microsecond to 60 second range.

## Files

- `CO_interrupt_STM32.h` - Header file with function declarations
- `CO_interrupt_STM32.c` - Implementation of timer and interrupt management functions

## Features

### Timer Management
- **High-precision timing**: DWT-based microsecond precision
- **Wide range**: 1 microsecond to 60 seconds
- **Automatic prescaler calculation**: Optimized for performance
- **Hybrid algorithm**: 32-bit for fast periods, 64-bit for large periods

### Interrupt Synchronization
- **FreeRTOS integration**: Binary semaphore for task synchronization
- **ISR-safe operations**: Proper interrupt handling with task yielding
- **Debug support**: GPIO toggle for interrupt visualization

## Usage Examples

### Initialization
```c
#include "CO_interrupt_STM32.h"

// Initialize timer module
CO_timer_init_STM32();

// Initialize interrupt synchronization
CO_interrupt_init();
```

### Timer Functions
```c
// Set timer period with automatic prescaler calculation
setTimerPeriod_us(1000);      // 1 ms
setTimerPeriod_us(1000000);   // 1 second  
setTimerPeriod_us(30000000);  // 30 seconds

// Get timer range information
CO_timer_range_t range;
CO_timer_getRange(&range);
printf("Min: %lu us, Max: %lu us\n", range.min_period_us, range.max_period_us);

// Get timing statistics
CO_TimingStats_t stats;
CO_timer_getTimingStats(&stats);
printf("Next event: %lu us\n", stats.nextTimerEvent_us);
```

### Interrupt Synchronization
```c
// In task function (replaces old mutex code)
void canopen_app_interrupt(void)
{
    if (CO_interrupt_take())  // Replaces: xSemaphoreTake(CO_Interrupt_Mutex, portMAX_DELAY)
    {
        // CANopen processing code here
        // ...
    }
}

// In timer interrupt handler (replaces old ISR code)
void TIM1_UP_IRQHandler(void)
{
    if (__HAL_TIM_GET_FLAG(&htim1, TIM_FLAG_UPDATE) != RESET)
    {
        __HAL_TIM_CLEAR_IT(&htim1, TIM_IT_UPDATE);
        
        // Give semaphore and yield if needed
        CO_interrupt_giveFromISR();  // Replaces manual semaphore + GPIO + yield code
    }
}
```

## Migration from Old Code

### Replace Mutex Operations
**Old code:**
```c
// Initialization
CO_Interrupt_Mutex = xSemaphoreCreateBinary();

// Task waiting
if (xSemaphoreTake(CO_Interrupt_Mutex, portMAX_DELAY) == pdTRUE)
{
    // Processing...
}

// ISR signaling
BaseType_t xHigherPriorityTaskWoken = pdFALSE;
xSemaphoreGiveFromISR(CO_Interrupt_Mutex, &xHigherPriorityTaskWoken);
HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);
portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
```

**New code:**
```c
// Initialization
CO_interrupt_init();

// Task waiting
if (CO_interrupt_take())
{
    // Processing...
}

// ISR signaling
CO_interrupt_giveFromISR();
```

## Performance

- **Fast path**: 32-bit arithmetic for periods ≤ 59ms (~10-15 CPU cycles)
- **Slow path**: 64-bit arithmetic for periods > 59ms (~150-250 CPU cycles)
- **Automatic optimization**: 99% of CANopen cases use fast path

## Range and Precision

- **Minimum period**: ~1 microsecond
- **Maximum period**: ~59.65 seconds
- **Resolution**: Depends on timer clock frequency
- **Accuracy**: High precision with automatic prescaler optimization

## Integration

Include the module in your CANopen application:

1. Add files to your project
2. Include header: `#include "CO_interrupt_STM32.h"`
3. Initialize in your main function
4. Replace old mutex code with new functions
5. Update interrupt handlers to use new ISR function

## Migration Summary

### Files Updated

**main.c:**
- Removed: `xSemaphoreHandle CO_Interrupt_Mutex;`
- Removed: `CO_Interrupt_Mutex = xSemaphoreCreateBinary();`
- Added: `#include "CO_interrupt_STM32.h"`
- Replaced ISR code with: `CO_interrupt_giveFromISR();`

**CO_app_STM32.c:**
- Added: `#include "CO_interrupt_STM32.h"`
- Added: `CO_interrupt_init();` in initialization
- Replaced: `xSemaphoreTake(CO_Interrupt_Mutex, portMAX_DELAY) == pdTRUE` with `CO_interrupt_take()`
- Removed: `xSemaphoreGive(CO_Interrupt_Mutex);`

### Benefits of Migration

1. **Cleaner code**: Reduced from 5+ lines to 1 function call
2. **Better encapsulation**: All interrupt logic in one module
3. **Easier maintenance**: Centralized interrupt management
4. **Type safety**: No direct semaphore handle exposure
5. **Error handling**: Built-in NULL checks and validation
