/*
 * CANopen Dynamic Timer Management for STM32
 * 
 * @file        CO_timer_STM32.h
 * <AUTHOR> Assistant
 * @date        2024
 * 
 * This file is part of CANopenNode, an opensource CANopen Stack.
 */

#ifndef CO_TIMER_STM32_H
#define CO_TIMER_STM32_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"
#include "CANopen.h"

/* Timer statistics structure */
typedef struct {
    uint32_t lastTimeDifference_us;
    uint32_t nextTimerEvent_us;
    uint32_t currentTime_us;
    bool_t timerInitialized;
} CO_TimingStats_t;

/* Function to get current time in microseconds */
uint32_t getCurrentTime_us(void);

/* Function to set timer period dynamically with automatic prescaler calculation */
void setTimerPeriod_us(uint32_t period_us);

/* Get timer period range information */
typedef struct {
    uint32_t min_period_us;     /* Minimum supported period in microseconds */
    uint32_t max_period_us;     /* Maximum supported period in microseconds */
    uint32_t resolution_us;     /* Best resolution in microseconds */
    uint32_t timer_clock_hz;    /* Timer clock frequency */
} CO_timer_range_t;

/* Get timer period range and resolution */
void CO_timer_getRange(CO_timer_range_t *range);

/* Initialize timer management */
void CO_timer_init_STM32(void);

/* Process CANopen interrupt with dynamic timing */
void CO_timer_processInterrupt(bool_t *syncWas, uint32_t *timeDifference_us, uint32_t *timerNext_us);

/* Get the next scheduled timer event in microseconds */
uint32_t CO_timer_getNextTimerEvent_us(void);

/* Process timer next event after CANopen functions */
void CO_timer_processNextEvent(uint32_t timerNext_us_from_canopen);

/* Get timing statistics for debugging */
void CO_timer_getTimingStats(CO_TimingStats_t *stats);

/* Test function to demonstrate timer range capabilities */
void CO_timer_testRange(void);

#ifdef __cplusplus
}
#endif

#endif /* CO_TIMER_STM32_H */
