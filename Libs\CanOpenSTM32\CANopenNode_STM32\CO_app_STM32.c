/*
 * CANopen main program file.
 *
 * This file is a template for other microcontrollers.
 *
 * @file        main_generic.c
 * <AUTHOR> 	2022
 * 				<PERSON><PERSON> Paternoster	2021
 * @copyright   2021 <PERSON><PERSON> Paternoster
 *
 * This file is part of CANopenNode, an opensource CANopen Stack.
 * Project home page is <https://github.com/CANopenNode/CANopenNode>.
 * For more information on CANopen see <http://www.can-cia.org/>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "CO_app_STM32.h"
#include "CANopen.h"
#include "main.h"
#include <stdio.h>
#include "stm32f1xx.h"

#include "OD.h"
#if (CO_CONFIG_STORAGE) & CO_CONFIG_STORAGE_ENABLE
#include <storage/CO_storageEeprom.h>
#include <CO_eeprom.h>
#include "CO_eepromStorageModeleT.h"
#include "CO_storage.h"
storageModyle_t eepromModele;
CO_storage_t storage;
#endif


CANopenNodeSTM32*
    canopenNodeSTM32; // It will be set by canopen_app_init and will be used across app to get access to CANOpen objects

/* Printf function of CanOpen app */


/* default values for CO_CANopenInit() */
#define NMT_CONTROL                                                                                                    \
    CO_NMT_STARTUP_TO_OPERATIONAL                                                                                      \
    | CO_NMT_ERR_ON_ERR_REG | CO_ERR_REG_GENERIC_ERR
#define FIRST_HB_TIME        500
#define SDO_SRV_TIMEOUT_TIME 1000
#define SDO_CLI_TIMEOUT_TIME 500
#define SDO_CLI_BLOCK        false
#define OD_STATUS_BITS       NULL

/* Global variables and objects */
CO_t* CO = NULL; /* CANopen object */

// Global variables
uint32_t time_old, time_current;
CO_ReturnError_t err;
static uint32_t storageInitError = 0;

/* This function will basically setup the CANopen node */



#if (CO_CONFIG_STORAGE) & CO_CONFIG_STORAGE_ENABLE

    CO_storage_entry_t storageEntries[] = {{.addr = &OD_EEPROM,
                                            .len = sizeof(OD_EEPROM),
                                            .subIndexOD = 2,
                                            .attr = CO_storage_cmd | CO_storage_restore | CO_storage_auto,
                                            .addrNV = NULL}};
    uint8_t storageEntriesCount = sizeof(storageEntries) / sizeof(storageEntries[0]);


static ODR_t storeEeprom(CO_storage_entry_t *entry, CO_CANmodule_t *CANmodule) {
	bool_t writeOk;
	writeOk = CO_eeprom_writeBlock(entry->storageModule, entry->addr,
			entry->eepromAddr, entry->len);
	entry->crc = crc16_ccitt(entry->addr, entry->len, 0);

	/* Verify, if data in eeprom are equal */
	uint16_t crc_read = CO_eeprom_getCrcBlock(entry->storageModule,
			entry->eepromAddr, entry->len);
	if (entry->crc != crc_read || !writeOk) {
		return ODR_HW;
	}

	/* Write signature (see CO_storageEeprom_init() for info) */
	uint16_t signatureOfEntry = (uint16_t) entry->len;
	uint32_t signature = (((uint32_t) entry->crc) << 16) | signatureOfEntry;
	writeOk = CO_eeprom_writeBlock(entry->storageModule, (uint8_t*) &signature,
			entry->eepromAddrSignature, sizeof(signature));

	/* verify signature and write */
	uint32_t signatureRead;
	CO_eeprom_readBlock(entry->storageModule, (uint8_t*) &signatureRead,
			entry->eepromAddrSignature, sizeof(signatureRead));
	if (signature != signatureRead || !writeOk) {
		return ODR_HW;
	}

	return ODR_OK;
}
#endif

int canopen_app_init(CANopenNodeSTM32* _canopenNodeSTM32) {

    // Keep a copy global reference of canOpenSTM32 Object
    canopenNodeSTM32 = _canopenNodeSTM32;



    /* Allocate memory */
    CO_config_t* config_ptr = NULL;
#ifdef CO_MULTIPLE_OD
    /* example usage of CO_MULTIPLE_OD (but still single OD here) */
    CO_config_t co_config = {0};
    OD_INIT_CONFIG(co_config); /* helper macro from OD.h */
    co_config.CNT_LEDS = 1;
    co_config.CNT_LSS_SLV = 1;
    config_ptr = &co_config;
#endif /* CO_MULTIPLE_OD */

    uint32_t heapMemoryUsed;
    CO = CO_new(config_ptr, &heapMemoryUsed);
    if (CO == NULL) {

        return 1;
    } else {

    }

    canopenNodeSTM32->canOpenStack = CO;

#if (CO_CONFIG_STORAGE) & CO_CONFIG_STORAGE_ENABLE


    err=CO_storageEeprom_init(&storage, CO->CANmodule,&eepromModele, OD_ENTRY_H1010_storeParameters,
                                   OD_ENTRY_H1011_restoreDefaultParameters, storageEntries, storageEntriesCount,
                                   &storageInitError);


    //CO_storageEeprom_auto_process(&storage, true);
    if (err != CO_ERROR_NO && err != CO_ERROR_DATA_CORRUPT) {
        return 2;
    }

    if ( err == CO_ERROR_DATA_CORRUPT)
    {

    	ODR_t ret2=storeEeprom(storageEntries,CO->CANmodule);
    	if (ret2==ODR_OK)
    	{
    	    err=CO_storageEeprom_init(&storage, CO->CANmodule,&eepromModele, OD_ENTRY_H1010_storeParameters,
    	                                   OD_ENTRY_H1011_restoreDefaultParameters, storageEntries, storageEntriesCount,
    	                                   &storageInitError);
    	}
    }
    if (err != CO_ERROR_NO && err != CO_ERROR_DATA_CORRUPT) {
        return 2;
    }

#endif

    canopen_app_resetCommunication();
    return 0;
}

int
canopen_app_resetCommunication() {
    /* CANopen communication reset - initialize CANopen objects *******************/

    /* Wait rt_thread. */
    CO->CANmodule->CANnormal = false;

    /* Enter CAN configuration. */
    CO_CANsetConfigurationMode((void*)canopenNodeSTM32);
    CO_CANmodule_disable(CO->CANmodule);

    /* initialize CANopen */
    err = CO_CANinit(CO, canopenNodeSTM32, 0); // Bitrate for STM32 microcontroller is being set in MXCube Settings
    if (err != CO_ERROR_NO) {
        return 1;
    }

#if ((CO_CONFIG_LSS) & (CO_CONFIG_LSS_SLAVE | CO_CONFIG_LSS_MASTER))
    CO_LSS_address_t lssAddress = {.identity = {.vendorID = OD_PERSIST_COMM.x1018_identity.vendor_ID,
                                                .productCode = OD_PERSIST_COMM.x1018_identity.productCode,
                                                .revisionNumber = OD_PERSIST_COMM.x1018_identity.revisionNumber,
                                                .serialNumber = OD_PERSIST_COMM.x1018_identity.serialNumber}};
    err = CO_LSSinit(CO, &lssAddress, &canopenNodeSTM32->desiredNodeID, &canopenNodeSTM32->baudrate);

    if (err != CO_ERROR_NO) {
        log_printf("Error: LSS slave initialization failed: %d\n", err);
        return 2;
    }
#endif
    canopenNodeSTM32->activeNodeID = canopenNodeSTM32->desiredNodeID;
    uint32_t errInfo = 0;

    err = CO_CANopenInit(CO,                   /* CANopen object */
                         NULL,                 /* alternate NMT */
                         NULL,                 /* alternate em */
                         OD,                   /* Object dictionary */
                         OD_STATUS_BITS,       /* Optional OD_statusBits */
                         NMT_CONTROL,          /* CO_NMT_control_t */
                         FIRST_HB_TIME,        /* firstHBTime_ms */
                         SDO_SRV_TIMEOUT_TIME, /* SDOserverTimeoutTime_ms */
                         SDO_CLI_TIMEOUT_TIME, /* SDOclientTimeoutTime_ms */
                         SDO_CLI_BLOCK,        /* SDOclientBlockTransfer */
                         canopenNodeSTM32->activeNodeID, &errInfo);
    if (err != CO_ERROR_NO && err != CO_ERROR_NODE_ID_UNCONFIGURED_LSS) {
        if (err == CO_ERROR_OD_PARAMETERS) {

        } else {

        }
        return 3;
    }

    err = CO_CANopenInitPDO(CO, CO->em, OD, canopenNodeSTM32->activeNodeID, &errInfo);
    if (err != CO_ERROR_NO) {
        if (err == CO_ERROR_OD_PARAMETERS) {

        } else {

        }
        return 4;
    }

    /* Create semaphore for CANopen timer events */
    if (CO_Timer_Semaphore == NULL) {
        CO_Timer_Semaphore = xSemaphoreCreateBinary();
    }

    /* Configure Timer interrupt function for dynamic execution */
    HAL_TIM_Base_Start_IT(canopenNodeSTM32->timerHandle); // Dynamic interrupt

    /* Configure CAN transmit and receive interrupt */

    /* Configure CANopen callbacks, etc */
    if (!CO->nodeIdUnconfigured) {

#if (CO_CONFIG_STORAGE) & CO_CONFIG_STORAGE_ENABLE
        if (storageInitError != 0) {
            CO_errorReport(CO->em, CO_EM_NON_VOLATILE_MEMORY, CO_EMC_HARDWARE, storageInitError);
        }
#endif
    } else {

    }

    /* start CAN */
    CO_CANsetNormalMode(CO->CANmodule);

    fflush(stdout);
    time_old = time_current = HAL_GetTick();
    return 0;
}

void
canopen_app_process() {
    /* loop for normal program execution ******************************************/
    /* get time difference since last function call */



	time_current = HAL_GetTick();

    if ((time_current - time_old) > 0) { // Make sure more than 1ms elapsed
        /* CANopen process */
        CO_NMT_reset_cmd_t reset_status;
        uint32_t timeDifference_us = (time_current - time_old) * 1000;
        time_old = time_current;
        reset_status = CO_process(CO, false, timeDifference_us, NULL);
        //canopenNodeSTM32->outStatusLEDRed = CO_LED_RED(CO->LEDs, CO_LED_CANopen);
        //canopenNodeSTM32->outStatusLEDGreen = CO_LED_GREEN(CO->LEDs, CO_LED_CANopen);
#if (CO_CONFIG_STORAGE) & CO_CONFIG_STORAGE_ENABLE
        CO_storageEeprom_auto_process(&storage, false);
#endif
        if (reset_status == CO_RESET_COMM) {
            /* delete objects from memory */
            CO_CANsetConfigurationMode((void*)canopenNodeSTM32);
            CO_delete(CO);

            canopen_app_resetCommunication(); // Reset Communication routine
        } else if (reset_status == CO_RESET_APP) {

            HAL_NVIC_SystemReset(); // Reset the STM32 Microcontroller
        }
    }
}

extern xSemaphoreHandle CO_Interrupt_Mutex;

/* Semaphore for CANopen timer events */
xSemaphoreHandle CO_Timer_Semaphore = NULL;

/* Static variables for dynamic timer management */
static uint32_t lastTime_us = 0;
static uint32_t timerNext_us = 1000; // Default 1ms
static bool_t timerInitialized = false;
static uint32_t lastTimeDifference_us = 0;

/* Function to get current time in microseconds */
static uint32_t getCurrentTime_us(void)
{
    /* Use DWT (Data Watchpoint and Trace) cycle counter for high precision */
    /* This provides microsecond precision on Cortex-M3/M4 */
    static uint32_t initialized = 0;

    if (!initialized) {
        /* Enable DWT */
        CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
        DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
        DWT->CYCCNT = 0;
        initialized = 1;
    }

    /* Get system clock frequency */
    uint32_t SystemCoreClock_MHz = SystemCoreClock / 1000000;

    /* Convert CPU cycles to microseconds */
    return DWT->CYCCNT / SystemCoreClock_MHz;
}

/* Function to set timer period dynamically */
static void setTimerPeriod_us(uint32_t period_us)
{
    if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL) {
        TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

        /* Stop timer */
        HAL_TIM_Base_Stop_IT(htim);

        /* Get timer clock frequency */
        uint32_t timer_clock_hz = SystemCoreClock;

        /* Account for APB prescaler */
        if (htim->Instance == TIM1) {
            timer_clock_hz = HAL_RCC_GetPCLK2Freq();
            if ((RCC->CFGR & RCC_CFGR_PPRE2) != 0) timer_clock_hz *= 2;
        } else {
            timer_clock_hz = HAL_RCC_GetPCLK1Freq();
            if ((RCC->CFGR & RCC_CFGR_PPRE1) != 0) timer_clock_hz *= 2;
        }

        /* Calculate prescaler and period for desired microsecond timing */
        uint32_t prescaler = htim->Init.Prescaler + 1;
        uint32_t timer_freq_hz = timer_clock_hz / prescaler;

        /* Calculate period in timer ticks */
        uint32_t period_ticks = (timer_freq_hz * period_us) / 1000000;

        /* Limit period to reasonable bounds */
        if (period_ticks < 10) period_ticks = 10;           // Minimum ~10 ticks
        if (period_ticks > 65535) period_ticks = 65535;     // Maximum for 16-bit timer

        /* Set new period */
        __HAL_TIM_SET_AUTORELOAD(htim, period_ticks - 1);
        __HAL_TIM_SET_COUNTER(htim, 0);

        /* Restart timer */
        HAL_TIM_Base_Start_IT(htim);
    }
}

/* Thread function executes in constant intervals, this function can be called from FreeRTOS tasks or Timers ********/
void canopen_app_interrupt(void)
{
	if (xSemaphoreTake(CO_Interrupt_Mutex, portMAX_DELAY) == pdTRUE)
	{
		CO_LOCK_OD(CO->CANmodule);
		if (!CO->nodeIdUnconfigured && CO->CANmodule->CANnormal)
		{
			bool_t syncWas = false;
			uint32_t currentTime_us = getCurrentTime_us();
			uint32_t timeDifference_us;

			/* Initialize timer on first call */
			if (!timerInitialized) {
				lastTime_us = currentTime_us;
				timerInitialized = true;
				timeDifference_us = 1000; // Default 1ms for first call
			} else {
				/* Calculate real time difference since last function call */
				timeDifference_us = currentTime_us - lastTime_us;
				/* Prevent overflow and unreasonable values */
				if (timeDifference_us > 100000) timeDifference_us = 100000; // Max 100ms
				if (timeDifference_us < 100) timeDifference_us = 100;       // Min 100us
			}

			/* Initialize timerNext_us with maximum value */
			timerNext_us = UINT32_MAX;

			#if (CO_CONFIG_SYNC) & CO_CONFIG_SYNC_ENABLE
					syncWas = CO_process_SYNC(CO, timeDifference_us, &timerNext_us);
			#endif
			#if (CO_CONFIG_PDO) & CO_CONFIG_RPDO_ENABLE
					CO_process_RPDO(CO, syncWas, timeDifference_us, &timerNext_us);
			#endif
			#if (CO_CONFIG_PDO) & CO_CONFIG_TPDO_ENABLE
					CO_process_TPDO(CO, syncWas, timeDifference_us, &timerNext_us);
			#endif

			/* Update last time */
			lastTime_us = currentTime_us;
			lastTimeDifference_us = timeDifference_us;

			/* Set timer for next interrupt based on CANopen scheduling */
			if (timerNext_us == UINT32_MAX || timerNext_us == 0) {
				timerNext_us = 1000; // Default 1ms if no specific timing required
			}
			setTimerPeriod_us(timerNext_us);

			/* Further I/O or nonblocking application code may go here. */
		}
		CO_UNLOCK_OD(CO->CANmodule);
		xSemaphoreGive(CO_Interrupt_Mutex);
	}
}

/* Get the next scheduled timer event in microseconds */
uint32_t canopen_app_getNextTimerEvent_us(void)
{
    return timerNext_us;
}

/* Force immediate execution of CANopen interrupt processing */
void canopen_app_forceInterrupt(void)
{
    canopen_app_interrupt();
}

/* Get timing statistics for debugging */
void canopen_app_getTimingStats(CO_TimingStats_t *stats)
{
    if (stats != NULL) {
        stats->lastTimeDifference_us = lastTimeDifference_us;
        stats->nextTimerEvent_us = timerNext_us;
        stats->currentTime_us = getCurrentTime_us();
        stats->timerInitialized = timerInitialized;
    }
}
