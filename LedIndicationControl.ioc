#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
CAN.BS1=CAN_BS1_15TQ
CAN.BS2=CAN_BS2_2TQ
CAN.CalculateBaudRate=500000
CAN.CalculateTimeBit=2000
CAN.CalculateTimeQuantum=111.11111111111111
CAN.IPParameters=CalculateTimeQuantum,CalculateTimeBit,CalculateBaudRate,Prescaler,BS1,BS2
CAN.Prescaler=4
Dma.Request0=TIM2_CH2/CH4
Dma.RequestsNb=1
Dma.TIM2_CH2/CH4.0.Direction=DMA_MEMORY_TO_PERIPH
Dma.TIM2_CH2/CH4.0.Instance=DMA1_Channel7
Dma.TIM2_CH2/CH4.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.TIM2_CH2/CH4.0.MemInc=DMA_MINC_ENABLE
Dma.TIM2_CH2/CH4.0.Mode=DMA_CIRCULAR
Dma.TIM2_CH2/CH4.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.TIM2_CH2/CH4.0.PeriphInc=DMA_PINC_DISABLE
Dma.TIM2_CH2/CH4.0.Priority=DMA_PRIORITY_LOW
Dma.TIM2_CH2/CH4.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority
FREERTOS.IPParameters=Tasks01,configUSE_NEWLIB_REENTRANT,configTOTAL_HEAP_SIZE
FREERTOS.Tasks01=defaultTask,0,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL
FREERTOS.configTOTAL_HEAP_SIZE=5000
FREERTOS.configUSE_NEWLIB_REENTRANT=1
File.Version=6
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=CAN
Mcu.IP1=DMA
Mcu.IP2=FREERTOS
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM1
Mcu.IP7=TIM2
Mcu.IP8=TIM3
Mcu.IPNb=9
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PD0-OSC_IN
Mcu.Pin1=PD1-OSC_OUT
Mcu.Pin10=VP_TIM1_VS_ClockSourceINT
Mcu.Pin11=VP_TIM3_VS_ClockSourceINT
Mcu.Pin2=PB12
Mcu.Pin3=PA13
Mcu.Pin4=PA14
Mcu.Pin5=PB3
Mcu.Pin6=PB8
Mcu.Pin7=PB9
Mcu.Pin8=VP_FREERTOS_VS_CMSIS_V1
Mcu.Pin9=VP_SYS_VS_tim4
Mcu.PinsNb=12
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.10.0
MxDb.Version=DB.6.0.100
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.CAN1_RX1_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.CAN1_SCE_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.DMA1_Channel7_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM1_UP_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TIM3_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TIM4_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM4_IRQn
NVIC.TimeBaseIP=TIM4
NVIC.USB_HP_CAN1_TX_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USB_LP_CAN1_RX0_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PB12.Locked=true
PB12.Signal=GPIO_Output
PB3.GPIOParameters=GPIO_Speed
PB3.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB3.Locked=true
PB3.Signal=S_TIM2_CH2
PB8.Locked=true
PB8.Mode=CAN_Activate
PB8.Signal=CAN_RX
PB9.Locked=true
PB9.Mode=CAN_Activate
PB9.Signal=CAN_TX
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.5
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=false
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=LedIndicationControl.ioc
ProjectManager.ProjectName=LedIndicationControl
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-false,4-MX_TIM2_Init-TIM2-false-HAL-false,5-MX_CAN_Init-CAN-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM3_Init-TIM3-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SH.S_TIM2_CH2.0=TIM2_CH2,PWM Generation2 CH2
SH.S_TIM2_CH2.ConfNb=1
TIM1.IPParameters=Prescaler,Period
TIM1.Period=79
TIM1.Prescaler=899
TIM2.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM2.IPParameters=Channel-PWM Generation2 CH2
TIM3.IPParameters=Period,Prescaler
TIM3.Period=79
TIM3.Prescaler=899
VP_FREERTOS_VS_CMSIS_V1.Mode=CMSIS_V1
VP_FREERTOS_VS_CMSIS_V1.Signal=FREERTOS_VS_CMSIS_V1
VP_SYS_VS_tim4.Mode=TIM4
VP_SYS_VS_tim4.Signal=SYS_VS_tim4
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
board=custom
rtos.0.ip=FREERTOS
isbadioc=false
