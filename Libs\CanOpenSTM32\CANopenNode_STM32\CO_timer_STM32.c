/*
 * CANopen Dynamic Timer Management for STM32 Implementation
 * 
 * @file        CO_timer_STM32.c
 * <AUTHOR> Assistant
 * @date        2024
 * 
 * This file is part of CANopenNode, an opensource CANopen Stack.
 */

#include "CO_timer_STM32.h"
#include "CO_app_STM32.h"
#include "stm32f1xx.h"

/* Function to get current time in microseconds */
uint32_t getCurrentTime_us(void)
{
    /* Use DWT (Data Watchpoint and Trace) cycle counter for high precision */
    /* This provides microsecond precision on Cortex-M3/M4 */
    static uint32_t initialized = 0;
    
    if (!initialized) {
        /* Enable DWT */
        CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
        DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
        DWT->CYCCNT = 0;
        initialized = 1;
    }
    
    /* Get system clock frequency */
    uint32_t SystemCoreClock_MHz = SystemCoreClock / 1000000;
    
    /* Convert CPU cycles to microseconds */
    return DWT->CYCCNT / SystemCoreClock_MHz;
}

/* Function to set timer period dynamically */
void setTimerPeriod_us(uint32_t period_us)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;
    
    if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL) {
        TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;
        
        /* Stop timer */
        HAL_TIM_Base_Stop_IT(htim);
        
        /* Get timer clock frequency */
        uint32_t timer_clock_hz = SystemCoreClock;
        
        /* Account for APB prescaler */
        if (htim->Instance == TIM1) {
            timer_clock_hz = HAL_RCC_GetPCLK2Freq();
            if ((RCC->CFGR & RCC_CFGR_PPRE2) != 0) timer_clock_hz *= 2;
        } else {
            timer_clock_hz = HAL_RCC_GetPCLK1Freq();
            if ((RCC->CFGR & RCC_CFGR_PPRE1) != 0) timer_clock_hz *= 2;
        }
        
        /* Calculate prescaler and period for desired microsecond timing */
        uint32_t prescaler = htim->Init.Prescaler + 1;
        uint32_t timer_freq_hz = timer_clock_hz / prescaler;
        
        /* Calculate period in timer ticks */
        uint32_t period_ticks = (timer_freq_hz * period_us) / 1000000;
        
        /* Limit period to reasonable bounds */
        if (period_ticks < 10) period_ticks = 10;           // Minimum ~10 ticks
        if (period_ticks > 65535) period_ticks = 65535;     // Maximum for 16-bit timer
        
        /* Set new period */
        __HAL_TIM_SET_AUTORELOAD(htim, period_ticks - 1);
        __HAL_TIM_SET_COUNTER(htim, 0);
        
        /* Restart timer */
        HAL_TIM_Base_Start_IT(htim);
    }
}

/* Initialize timer management */
void CO_timer_init_STM32(void)
{
    /* Initialize DWT by calling getCurrentTime_us once */
    getCurrentTime_us();
}
