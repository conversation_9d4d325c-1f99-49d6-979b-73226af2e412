/**
 * Description of the coding style for the source files.
 *
 * @file        codingStyle
 * @ingroup     codingStyle
 * <AUTHOR>
 * @copyright   2020 name
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef XYZ_H
#define XYZ_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @defgroup codingStyle Description of coding style
 * @ingroup parentGroup
 * @{
 *
 * Contents of this file should be the base for .h source file, except function
 * body at the end.
 *
 * ###Style
 *  - Style is based on https://github.com/MaJerle/c-code-style
 *  - Indent size is 4 spaces, no tabs.
 *  - Line width is 80 characters.
 *  - Some (old) code may not be formatted according to the rules. Try to avoid
 *    unnecessary changes based on individual taste.
 *
 * ###Doxygen
 * Documentation is generated by doxygen.
 * Doxygen comment starts with /**. /**< is used after member.
 * Documentation is usually in header.
 * Doxygen settings:
 *  - JAVADOC_AUTOBRIEF = YES.
 *  - See doxyfile for other settings.
 *
 * Doxygen specifics: If description of the structure member is one sentence
 * only, don't use period after the sentence.
 */


/**
 * Brief description of the object ends at this dot. Details follow
 * here.
 */
typedef struct {
    int8_t member1;   /**< Short description of the member 1 */
    uint16_t member2; /**< Note the '/**<' sequence after the member 2 */
    /** Long description of the variable stringMember. More description. */
    char_t stringMember[5];
} object1_t;


/**
 * Function example 1.
 *
 * This is global function. Local functions (and variables) used inside one file
 * are declared as static and not documented by Doxygen.
 *
 * @param thisObj Pointer to object. Function operates on this object (not on
 * global variables).
 * @param argument_2 Description of the argument.
 * @param argument_2 Description of the argument.
 * @param argument_4 Description of the argument.
 *
 * @return Some value.
 */
int32_t foo1(object1_t *thisObj,
             int32_t argument_2,
             uint16_t argument_3,
             float32_t argument_4)
{
    /* Comment */

    /* Multiline
     * comment.
     */

    if (xy == yz) { /* Comment. '//' comments are not allowed */
        a = b;
    } else {
        a = c;
    }

    switch (zx) {
    case 1:
        a = b;
        break;
    }
}

/** @} */

#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* XYZ_H */
