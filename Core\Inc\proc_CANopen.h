#ifndef INC_PROC_CANOPEN_H_
#define INC_PROC_CANOPEN_H_

/**
 * @addtogroup proc_CanOpen
 * @{
 */

#include "cmsis_os.h"
#include "task.h"
#include "tim.h"
#include "can.h"
#include "CO_app_STM32.h"
#include "CANOpenData.h"

void CANopenProcInit(void);							///< Initializing CANopen tasks
void StartRealTimeProc(void const *arguments);		///< Real time CANopen task
void StartMainlineProc(void const *arguments);		///< Mainline CANopen task

CANOpenData_t CANopenProc_GetData(void);			///< Return CANOpenData_t
CANOpenData_t* CANopenProc_GetDataP(void);			///< Return pointer to CANOpenData_t
void CANopenProc_TPDOsendRequest(uint8_t TPDOid);	///< Send TPDO
void CANopenProc_SetODTimer(uint32_t value);		///< Set OD_TIMER

/**
 * @brief Check the RPDOCallback flag
 * @note  The function resets the flag when called
 */
bool_t CANopenProc_CheckRPDOCallback();

/**
 * @brief RPDO Callback
 * @warning It works as an interrupt and is therefore bad
 */
void RPDO_Callback(void *object);

/**
 * @brief   Custom RPDO Callback
 * @details This function is a replacement for RPDO_Callback
 * @todo    The current implementation of this feature is a temporary solution.
 */
void RPDO_Callback_Custom(void);

/// @cond
void _SetData(void);
void _SetModeParams(void);
/// @endcond

#endif

/** @} */
