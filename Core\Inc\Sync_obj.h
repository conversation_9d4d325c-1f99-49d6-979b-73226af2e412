#ifndef INC_SYNC_OBJ_H_
#define INC_SYNC_OBJ_H_

#include "CO_driver_target.h"
#include "Config.h"
#include "proc_LedMode.h"

/**
 * @addtogroup Sync_obj
 * @details    This object is needed to initiate synchronization by event
 * 			   This object has two types of events:
 * 			   - Event by period
 * 			   - Event by mode change
 * @{
 */

/// @brief Sync object
typedef struct
{
	uint32_t PeriodCounter;  ///< The counter for the event by period
} sync_obj;

/// @brief Update the event by period counter
void Sync_PeriodUpdate(sync_obj *sync);
/**
 * @brief   Return true, if period Event
 * @details This method checks whether the Period Counter has exceeded
 * 		    the SYNC_PERIOD value, if so, return true
 * @note    SYNC_PERIOD is defined in Config.h
 */
bool_t Sync_PeriodEvent(sync_obj *sync);
/**
 * @brief   Return true, if change mode Event
 * @details Checks if the procLedMode_obj Mode has changed,
 *          and returns true if changed
 * @param   mode LedMode object
 */
bool_t Sync_ChangeModeEvent(sync_obj *sync, procLedMode_obj *mode);


/** @} */

#endif
