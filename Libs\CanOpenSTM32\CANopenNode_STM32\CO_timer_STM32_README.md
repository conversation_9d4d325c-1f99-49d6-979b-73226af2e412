# CANopen Dynamic Timer Module for STM32

## Обзор

Модуль `CO_timer_STM32` выносит функции динамического управления таймером из основного файла `CO_app_STM32.c` в отдельный модуль для лучшей организации кода и поддержки.

## Файлы модуля

### CO_timer_STM32.h
Заголовочный файл с объявлениями функций:
- `getCurrentTime_us()` - Получение текущего времени в микросекундах
- `setTimerPeriod_us()` - Динамическая настройка периода таймера
- `CO_timer_init_STM32()` - Инициализация модуля таймера
- `CO_timer_processInterrupt()` - Обработка прерывания с динамическим тайминг
- `CO_timer_processNextEvent()` - Обработка следующего события таймера
- `CO_timer_getNextTimerEvent_us()` - Получение времени следующего события
- `CO_timer_getTimingStats()` - Получение статистики для отладки
- `CO_TimingStats_t` - Структура статистики таймера

### CO_timer_STM32.c
Файл реализации с функциями:
- Высокоточное измерение времени через DWT
- Динамическая настройка периода таймера TIM1
- Автоматическая инициализация DWT
- Управление статическими переменными таймера
- Обработка логики прерываний CANopen

## Использование

### Инициализация
```c
// В функции canopen_app_resetCommunication()
CO_timer_init_STM32();
```

### Обработка прерывания
```c
// В функции canopen_app_interrupt()
bool_t syncWas = false;
uint32_t timeDifference_us;
uint32_t timerNext_us;

CO_timer_processInterrupt(&syncWas, &timeDifference_us, &timerNext_us);
// ... вызовы CANopen функций ...
CO_timer_processNextEvent(timerNext_us);
```

### Получение статистики
```c
CO_TimingStats_t stats;
CO_timer_getTimingStats(&stats);
printf("Next event: %lu us\n", stats.nextTimerEvent_us);
```

## Интеграция

Модуль интегрирован в основной файл `CO_app_STM32.c`:

1. **Включение заголовка**: `#include "CO_timer_STM32.h"`
2. **Инициализация**: Вызов `CO_timer_init_STM32()` в `canopen_app_resetCommunication()`
3. **Использование функций**: Вызовы `getCurrentTime_us()` и `setTimerPeriod_us()` в `canopen_app_interrupt()`

## Преимущества модульного подхода

1. **Чистота кода**: Основной файл `CO_app_STM32.c` стал более читаемым
2. **Переиспользование**: Модуль можно использовать в других проектах
3. **Поддержка**: Легче поддерживать и модифицировать функции таймера
4. **Тестирование**: Модуль можно тестировать отдельно

## Функциональность

### Высокоточное время
- Использует DWT (Data Watchpoint and Trace) для микросекундной точности
- Автоматическая инициализация DWT при первом вызове
- Fallback на HAL_GetTick() если DWT недоступен

### Динамический таймер
- Автоматический расчет предделителей и периода
- Учет частоты шины APB1/APB2
- Ограничения на минимальные и максимальные значения
- Безопасная остановка и перезапуск таймера

## Совместимость

Модуль совместим с:
- STM32F1xx серией микроконтроллеров
- HAL библиотекой STM32
- CANopenNode библиотекой
- FreeRTOS

## Зависимости

- `stm32f1xx_hal.h` - HAL библиотека STM32
- `CO_app_STM32.h` - Основной заголовок CANopen приложения
- `CANopen.h` - CANopen библиотека
