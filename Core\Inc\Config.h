#ifndef INC_CONFIG_H_
#define INC_CONFIG_H_

/**
 * @addtogroup Config
 * @{
 */

// Configure
#define ID 				30
#define BOARD_NUMBER	1
#define CHANNEL_MODE	SINGLE
#define STRIP_LOCATION	AUTOMATIC_LOCATION
#define STRIP_BRIGTNESS 50
#define SYNC_PERIOD 	3000
#define NUMB_PIXELS		18 //!< 18 pixel - 30cm

/// @cond
// Calculating variables:
#define BOARD_TYPE
#define NODE_ID (ID + BOARD_NUMBER)
#define TPDO_COBID
#define RPDO_COBID
#define STRIP_LOCATION2

// Variables for choose:
// STRIP_LOCATION
#define FRONT_LEFT  1
#define FRONT_RIGHT 2
#define BACK_RIGHT	3
#define BACK_LEFT  	4
#define AUTOMATIC_LOCATION 4
// BOARD_TYPE
#define MASTER 	  0
#define SLAVE     1
// CHANNEL_MODE
#define SINGLE 0
#define DUAL   1


// Calculate BOARD_TYPE
#if   BOARD_NUMBER == 1
	#undef BOARD_TYPE
	#define BOARD_TYPE MASTER
#elif BOARD_NUMBER > 1
	#undef BOARD_TYPE
	#define BOARD_TYPE SLAVE
#endif

// Calculate PDO ID
#undef TPDO_COBID
#undef RPDO_COBID
#if BOARD_TYPE == MASTER
	#define TPDO_COBID (ID + 0x181)
	#define RPDO_COBID 0
#elif BOARD_TYPE == SLAVE
	#define TPDO_COBID 0
	#define RPDO_COBID (ID + 0x181)
#endif

// Calculate STRIP_LOCATION
#if STRIP_LOCATION == AUTOMATIC_LOCATION
    #undef STRIP_LOCATION
    #define STRIP_LOCATION BOARD_NUMBER
#endif

// Calculate STRIP_LOCATION2:
#if CHANNEL_MODE == DUAL
	#undef STRIP_LOCATION2
    #if STRIP_LOCATION == AUTOMATIC_LOCATION
        #define STRIP_LOCATION2 (NODE_ID + 1)
    #elif STRIP_LOCATION == FRONT_LEFT
        #define STRIP_LOCATION2 FRONT_RIGHT
    #elif STRIP_LOCATION == FRONT_RIGHT
        #define STRIP_LOCATION2 FRONT_LEFT
    #elif STRIP_LOCATION == BACK_LEFT
        #define STRIP_LOCATION2 BACK_RIGHT
    #elif STRIP_LOCATION == BACK_RIGHT
        #define STRIP_LOCATION2 BACK_LEFT
    #else
        #define STRIP_LOCATION2 -1
    #endif
#endif
#if CHANNEL_MODE == SINGLE
	#undef STRIP_LOCATION2
	#define STRIP_LOCATION2 -1
#endif
/// @endcond

#endif

/** @} */

/* If the OD has been changed by EDSEditor,
 * put RPDO_COBID in COB_IDUsedByRPDO = in the OD.c file
 */




// Doxygen description
/**
 * @addtogroup Config
 * @{
 *
 * # Configure
 * ### ID
 * Sets which nodeid the boards will be counted from
 *
 * ### BOARD_NUMBER
 * The board number is set here.<br>
 * Also sets the BOARD_TYPE parameter:
 * - if 0: set MASTER
 * - if 1-4: set SLAVE
 *
 * ### CHANNEL_MODE
 * - SINGLE
 * - DUAL
 *
 * ### STRIP_LOCATION
 * - FRONT_LEFT
 * - FRONT_RIGHT
 * - BACK_LEFT
 * - BACK_RIGHT
 * - AUTOMATIC_LOCATION
 *
 * ### STRIP_BRIGTNESS
 * Sets the brightness of the strip
 *
 * ### SYNC_PERIOD
 * Sets the time period for sending sync messages
 *
 * ### RPDO_COBID
 * Node-id of the message that the slave boards will read
 * @note If the OD has been changed by EDSEditor,
 * put RPDO_COBID in COB_IDUsedByRPDO = in the OD.c file
 *
 * ### NUMB_PIXELS
 * Sets the number of pixels to be used
 *
 * # Calculating variables
 * These parameters are calculated based on the configuration
 * ### BOARD_TYPE
 * - MASTER
 * - SLAVE
 *
 * ### NODE_ID
 * NODE_ID = ID + BOARD_NUMBER
 * ### STRIP_LOCATION2
 * STRIP_LOCATION for the second channel (if used)
 */
/** @} */
