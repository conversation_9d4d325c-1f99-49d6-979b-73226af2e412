#include "proc_CANopen.h"
#include "Config.h"

osThreadId RealTimeProcHandler;
osThreadId MainlineProcHandler;
void StartRealTimeProc(void const *arguments);
void StartMainlineProc(void const *arguments);

CANopenNodeSTM32 canopenNode;
CANOpenData_t CANOpenData;
bool_t isRPDOCallback;

void CANopenProcInit(void)
{
	canopenNode.CANHandle = &hcan;
	canopenNode.HWInitFunction = MX_CAN_Init;
	canopenNode.timerHandle = &htim1;
	canopenNode.desiredNodeID = NODE_ID;
	canopenNode.baudrate = 125;
	canopen_app_init(&canopenNode);

	#if BOARD_TYPE == MASTER
	OD_MODE = 5;
	#endif

	CO_RPDO_initCallbackPre(&canopenNode.canOpenStack->R<PERSON>O[0], NULL, RPDO_Callback);

	osThreadDef(RealTimeProc, StartRealTimeProc, osPriorityRealtime, 0, 128);
	RealTimeProcHandler = osThreadCreate(osThread(RealTimeProc), NULL);

	osThreadDef(MainlineProc, StartMainlineProc, osPriorityHigh, 0, 128);
	MainlineProcHandler = osThreadCreate(osThread(MainlineProc), NULL);
}

CO_NMT_internalState_t NMT_state;
void StartRealTimeProc(void const *arguments)
{
	isRPDOCallback = false;
	for(;;)
	{
		canopen_app_interrupt();
		RPDO_Callback_Custom();
		_SetData();

		#if BOARD_TYPE == MASTER
		_SetModeParams();
		#endif

		osDelay(1);
	}
}

uint32_t value;
void StartMainlineProc(void const *arguments)
{
	for(;;)
	{
		canopen_app_process();
		NMT_state = CO_NMT_getInternalState(canopenNode.canOpenStack->NMT);
		osDelay(1);
	}
}

CANOpenData_t CANopenProc_GetData(void)
{
	return CANOpenData;
}

CANOpenData_t* CANopenProc_GetDataP(void)
{
	CANOpenData_t* pCANOpenData = &CANOpenData;
	return pCANOpenData;
}

void CANopenProc_TPDOsendRequest(uint8_t TPDOid)
{
	CO_TPDOsendRequest(&canopenNode.canOpenStack->TPDO[TPDOid]);
}

void CANopenProc_SetODTimer(uint32_t value)
{
	CO_LOCK_OD(CO->CANmodule);
	OD_TIMER = value;
	CO_UNLOCK_OD(CO->CANmodule);
}

void RPDO_Callback(void *object)
{
	//isRPDOCallback = true;
}

void RPDO_Callback_Custom(void)
{
    static uint32_t currOdTimer = 0;
    static uint32_t prevOdTimer = 0;
    currOdTimer = OD_RAM.x6000_timer;

    if(prevOdTimer != currOdTimer)
    {
    	prevOdTimer = currOdTimer;
    	isRPDOCallback = true;
    }
}

bool_t CANopenProc_CheckRPDOCallback()
{
    if (isRPDOCallback)
    {
        isRPDOCallback = false;
        return true;
    }
    return false;
}

void _SetData(void)
{
	CO_LOCK_OD(CO->CANmodule);
	CANOpenData = OD_DATA();
	CO_UNLOCK_OD(CO->CANmodule);
}

void _SetModeParams(void)
{
	OD_MODE_PARAMS = OD_MODE_PARAMS_ARRAY[OD_MODE];
}


