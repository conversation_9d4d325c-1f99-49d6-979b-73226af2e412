#ifndef INC_PROC_MAIN_H_
#define INC_PROC_MAIN_H_

#include "cmsis_os.h"
#include "task.h"

/**
 * @addtogroup proc_Main
 * @brief      In this task, all interactions between objects occur
 * @{
 */

/// @brief Main task init
void MainProcInit();

/**
 * @brief   Main task
 * @details In this task, all interactions between objects occur
 * @code{.c}
#include "proc_Main.h"
#include "proc_CANopen.h"
#include "proc_LedMode.h"
#include "Sync_obj.h"

extern procLedMode_obj LedModeProcObject;
sync_obj Sync;
Timer_obj Timer;

void MainProcInit()
{
	osThreadDef(MainProc, StartMainProc, osPriorityHigh, 0, 128);
	MainProcHandler = osThreadCreate(osThread(MainProc), NULL);
}

void StartMainProc(void const *arguments)
{
	Timer_Init(&Timer, &htim3);
	Timer_Start_IT(&Timer);
	Timer.TimerCallback = TimerCallback;
	Timer.CallbackArg   = &Sync;

	LedModeProc_Init(&Timer, CANopenProc_GetDataP());

	for(;;)
	{
		#if BOARD_TYPE == MASTER
			_Master();
		#elif BOARD_TYPE == SLAVE
			_Slave();
		#endif

		osDelay(1);
	}
}

void _Master(void)
{
	_HandleSyncEvent();

	if(Sync_ChangeModeEvent(&Sync, &LedModeProcObject))
	{
		Timer_ResetCounter(&Timer);
		ARGB_EndAnimation(&LedModeProcObject.argbModule);
	}
}

void _Slave(void)
{
	_HandleRPDOCallback();

	if(LedModeProc_ModeIsChanged(&LedModeProcObject))
	{
		Timer_ResetCounter(&Timer);
		ARGB_EndAnimation(&LedModeProcObject.argbModule);
	}
}

void _HandleSyncEvent(void)
{
	if(Sync_PeriodEvent(&Sync) || Sync_ChangeModeEvent(&Sync, &LedModeProcObject))
	{
		CANopenProc_SetODTimer(Timer_GetCounter(&Timer));
		CANopenProc_TPDOsendRequest(TPDO_SYNCMessage);
		HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);
	}
}

void _HandleRPDOCallback(void)
{
	if(CANopenProc_CheckRPDOCallback())
	{
		Timer_SetCounter(&Timer,OD_TIMER);
		HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);
	}
}

void TimerCallback(void *arg)
{
	sync_obj *sync = (sync_obj *)arg;
	Sync_PeriodUpdate(sync);
}
 * @endcode
 */
void StartMainProc(void const *arguments);

/// @cond
void _Master(void);
void _Slave(void);
void _HandleSyncEvent(void);
void _HandleRPDOCallback(void);
void TimerCallback(void *arg);
/// @endcond

/** @} */

#endif
