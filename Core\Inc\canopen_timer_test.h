/*
 * CANopen Dynamic Timer Test Functions Header
 * 
 * This file contains declarations for test functions to demonstrate 
 * and verify the dynamic timer functionality for CANopen PDO synchronization.
 * 
 * @file        canopen_timer_test.h
 * <AUTHOR> Assistant
 * @date        2024
 */

#ifndef CANOPEN_TIMER_TEST_H
#define CANOPEN_TIMER_TEST_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

/* Test function declarations */

/**
 * @brief Initialize the CANopen timer test functions
 * @note Call this once during system initialization
 */
void canopen_timer_test_init(void);

/**
 * @brief Main test function - call periodically from main loop
 * @note This function runs various tests based on internal counter
 */
void canopen_timer_test_run(void);

/**
 * @brief Print current timing statistics to console
 * @note Useful for debugging timer behavior
 */
void canopen_timer_test_printStats(void);

/**
 * @brief Verify timer frequency accuracy
 * @note Calculates average timer period and compares to target
 */
void canopen_timer_test_verifyFrequency(void);

/**
 * @brief Test different timer periods
 * @note Forces different operating conditions to test timer adaptation
 */
void canopen_timer_test_forcePeriods(void);

/**
 * @brief Monitor timer event changes
 * @note Logs when the next timer event time changes
 */
void canopen_timer_test_monitorEvents(void);

#ifdef __cplusplus
}
#endif

#endif /* CANOPEN_TIMER_TEST_H */
