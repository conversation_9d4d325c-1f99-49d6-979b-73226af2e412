/**
 * @file 	proc_LedMode.h
 * <AUTHOR>
 * @date 	30-November-2023
 * @brief   FreeRTOS task source file for managing and displaying different LED patterns
 *          on an Addressable RGB (ARGB) strip based on various operation modes.
 */

/**
 * @addtogroup proc_LedMode
 * @{
 */

#ifndef INC_PROC_LEDMODE_H_
#define INC_PROC_LEDMODE_H_

#include "cmsis_os.h"
#include "task.h"
#include "ARGB.h"
#include "ARGB_Module.h"
#include "Config.h"
#include "TimerControl.h"
#include "CANOpenData.h"

// Animation settings
/**
 * @name Animation settings
 * @note PDLAY is a delay after animation
 * @{
 */
#define RUNNINGLINE_DELAY	    20
#define RUNNINGLINE_SIZEOFLINE  5
#define RUNNINGLINE_PDELAY		100
#define RUNNINGLINE_TYPE		RYNNINGLINE_NORMAL

#define FADE_MOVESTAND		    40

#define BLINK_SLEEP_DELAY	    1200

#define BLINK_EMERG_DELAY	    150
#define BLINK_EMERG_COUNT	    5
#define BLINK_EMERG_PDELAY	    50
/** @} */

/// Description of types of operating modes
enum WorkingMode {Off, Sleep, Emergency, Movement, Charging, Init, Аdjustment};

/// LedMode object structure
typedef struct
{
	enum WorkingMode Mode;
	uint8_t ModeParam;
	bool_t isAnimationMode;
	bool_t ModeIsChanged;
	argbModule_obj argbModule;
} procLedMode_obj;

/**
 * @name LedMode Task
 * @{
 */

/// Init LedMode Task
void LedModeProcInit(void);
/**
 * @brief   LedMode task
 * @details This function calls all private methods procLedMode_obj
 * 			& ARGB_SetTime
 * @code{.c}
 * void StartLedModeProc(void const *arguments)
 * {
 *     _ARGBInit();
 *
 *     for(;;)
 *     {
 *         _SetMode(&LedModeProcObject, pCANOpenData);
 *         _SetIsAnimationMode(&LedModeProcObject);
 *         _SetModeIsChanged(&LedModeProcObject);
 *
 *         // Set animation time
 *         ARGB_SetTime(&(LedModeProcObject.argbModule), Timer_GetCounter(pTimer));
 *
 *         ModeHandler(&LedModeProcObject);
 *         osDelay(1);
 *     }
 * }
 * @endcode
 */
void StartLedModeProc(void const *arguments);
/** @} */

/**
 * @name LedMode Public Methods
 * @{
 */

/**
 * @brief Init LedMode object
 *
 * @param Timer Pointer to Timer_obj
 * @param CANOpenData Pointer to CANOpenData_t
 */
void LedModeProc_Init(Timer_obj *Timer, CANOpenData_t *CANOpenData);
bool_t LedModeProc_IsAnimationCompleted(procLedMode_obj *this);		///< Return true if animation is complete
bool_t LedModeProc_ModeIsChanged(procLedMode_obj *this);			///< Return true if Mode is changed
/** @} */

/**
 * @name LedMode Private Methods
 * @{
 */

void _ARGBInit(void);	///< Init ARGB Libs

/**
 * @brief  Sets the procLedMode_obj Mode & ModeParam
 * 		   from the variable in CANOpenData_t
 *
 * @param    CANOpenData Pointer to CANOpenData_t
 */
void _SetMode(procLedMode_obj *this, CANOpenData_t *CANOpenData);
void _SetIsAnimationMode(procLedMode_obj *this);					///< Calculate & set procLedMode_obj isAnimationMode
void _SetModeIsChanged(procLedMode_obj *this);						///< Calculate & set procLedMode_obj ModeIsChanged
/** @} */

/**
 * @name LedMode Private Method: ModeHandler
 * @{
 */
void ModeHandler(procLedMode_obj *this);	///< Run a mode depending on the mode variable in procLedMode_obj
void _Mode_Off(procLedMode_obj *this);		///< The strip doesn't light
void _Mode_Init(procLedMode_obj *this);		///< Fading white color
void _Mode_Sleep(procLedMode_obj *this);	///< Displays a blinking cyan pattern

/**
 * @brief Has two states:
 * - Obstacle detected:	Light red
 * - Malfunction: Blink red (In the future, blink code error)
 */
void _Mode_Emergency(procLedMode_obj *this);

/**
 * @brief Has five states:
 * - Stand: A fading green color across the strip.
 * - Forward: A solid orange color.
 * - Back: Blinking orange color.
 * - Left: A running line in orange, only on the left strip.
 * - Right: A running line in orange, only on the right strip.
 */
void _Mode_Movement(procLedMode_obj *this);

void _Mode_Charging(procLedMode_obj *this);	///< Displays a solid white color
/** @} */

#endif

/** @} */ //proc_LedMode
