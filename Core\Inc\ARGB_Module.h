/**
 * @file 	ARGB_Module.h
 * <AUTHOR>
 * @date 	30-November-2023
 * @brief 	Module source file for the ARGB library by <PERSON> Geeks
 * @note	This module adds additional functions for the ARGB library
 */

/**
 * @addtogroup ARGB_Module
 * @
 * @brief This module adds additional functions for the ARGB library
 * @{
 */

#ifndef INC_ARGB_MODULE_H_
#define INC_ARGB_MODULE_H_

#include "ARGB.h"
#define STATE_MAX 3

// ---------------------SIMPLE ARGB FUNC-----------------------------------

/**
 * @addtogroup Simple_func Simple Func
 * @{
 */

/**
 * @brief Filling a specified strip range
 *
 * @param start The starting value of the range
 * @param end   The ending value of the range
 * @param r     The red color component (0-255)
 * @param g     The green color component (0-255)
 * @param b     The blue color component (0-255)
 */
void ARGB_FillRGB_Betwen(u16_t start, u16_t end, u8_t r, u8_t g, u8_t b);

/**
 * @brief   Fill LED strip range with color based on state
 * @details Has three states:
 * 			- No:  white color
 * 			- Yes: yellow color
 * 			- Extreme: red color
 *
 * @param State    The state for color selection.
 * @param startLen The starting index in the LED strip range.
 * @param end_len  The ending index in the LED strip range.
 */
void ARGB_FillThreeStates(uint8_t State, uint16_t startLen, uint16_t end_len);
/** @} */

// --------------------BLOCKING ANIMATIONS-------------------------------

/**
 * @addtogroup Blocking_animations Blocking Animations
 * @{
 */

// DELAY() configuration:
#define USE_FREERTOS			///< Setting up the configuration for the DELAY macro
#define USE_NOTIFY_DELAY_BREAK	///< Setting up the configuration for the DELAY macro

#if defined(USE_FREERTOS)
    #include "FreeRTOS.h"
    #include "cmsis_os.h"
    #include "task.h"
#endif

/**
 * @name  Animation functions
 * @{
 */

/**
 * @brief Animation: Running line
 *
 * Displays a running light line from the start to the end position with the color (r, g, b).
 *
 * @param start The starting index in the range of LEDs where the effect begins.
 * @param end The ending index in the range of LEDs where the effect ends.
 * @param r The red color component (0-255).
 * @param g The green color component (0-255).
 * @param b The blue color component (0-255).
 * @param delay_ms The delay in milliseconds between turning each LED on and off.
 */
void ARGB_RunningLine_BLOCKING(int start, int end, u8_t r, u8_t g, u8_t b, int delay_ms);
/**
 * @brief Animation: Blink
 *
 * Flashes a section of an ARGB LED strip between specified indices with a given color.
 *
 * @param start The starting index of the LEDs to blink (inclusive)
 * @param end The ending index of the LEDs to blink (inclusive).
 * @param r Red component of the color (0-255).
 * @param g Green component of the color (0-255).
 * @param b Blue component of the color (0-255).
 * @param delay_ms The delay in milliseconds between on and off switching of the LEDs.
 * @param BlinkCount The number of times to blink.
 */
void ARGB_Blink_BLOCKING(int start, int end, u8_t r, u8_t g, u8_t b, int delay_ms, int BlinkCount);
/**
 * @brief Animation: Fade
 *
 * Gradually changes the brightness of a specified section of an ARGB LED strip to create a fade effect.
 *
 * @param start The starting index of the LEDs for the fade effect.
 * @param end The ending index of the LEDs for the fade effect.
 * @param r Red component of the color (0-255).
 * @param g Green component of the color (0-255).
 * @param b Blue component of the color (0-255).
 * @param delay_ms The delay in milliseconds between each step of the fade effect.
 * @param currentBrightness The initial brightness level from which the fading starts and to which it returns. Range is typically from 0 (off) to 255 (maximum brightness).
 */
void ARGB_Fade_BLOCKING(u8_t start, u8_t end, u8_t r, u8_t g, u8_t b, u8_t delay_ms, u8_t currentBrightness);
/** @} */

/**
 * @name  Other
 * @{
 */

/**
 * @brief Interruptible delay function to stop animation
 *
 * This function implements an interruptible delay mechanism in FreeRTOS. It allows
 * a task to be suspended for a specified number of system timer ticks,
 * while also allowing the delay to be interrupted by notifications.
 *
 * @param delayTicks       Number of ticks to delay.
 * @param notifyingTask    Handle of the task to be notified to interrupt the delay.
 * @return                 Returns the notification value, which can be used
 *                         to determine the cause of the delay interruption.
 *
 * @note 				   If the function returns a non-zero value, it means that the delay was
 *       				   interrupted by a notification. A return value of zero indicates that the delay period elapsed as usual.
 */
#if defined(USE_FREERTOS)
uint32_t DelayWithNotification(uint32_t delayTicks, TaskHandle_t notifyingTask);
#endif

/**
 * @brief Configurable delay function
 *
 * Defines the DELAY macro based on project flags
 *
 * Options:
 * - USE_FREERTOS: Enable support for FreeRTOS.
 * - USE_OS_DELAY: Uses osDelay from CMSIS.
 * - USE_NOTIFY_DELAY: Uses DelayWithNotification from FreeRTOS (interruptible).
 * - USE_NOTIFY_DELAY_BREAK: Like above, but exits enclosing loop on notification.
 * - Default: Uses HAL_Delay (hardware timer-based delay).
 *
 * @note For USE_OS_DELAY, NOTIFY_DELAY & USE_NOTIFY_DELAY_BREAK ensure USE_FREERTOS is enabled.
 */
#define DELAY()
/** @} */
/** @} */

// ----------ARGB_MODULE_OBJ & NON_BLOCKING_ANIMATIONS---------

/**
 * @addtogroup NonBlocking_animations Non-Blocking Animations
 * @{
 */

/**
 * @brief    Animation object
 * @details  Each animation consists of the following components.
 *           - An animation step is one cycle during which some action occurs with the strip.
 *           The cycle time is set in the Animation_Update method.
 *           - An animation state is a set of steps.
 * @note     As an example, the blink method consists of two states:
 * 		     - 1st state: The method counts two cycles, and during each cycle, it either flashes or turns off the strip.
 * 		     - 2nd state: The method does nothing for one cycle.
 */
typedef struct {
    uint32_t start_time;	///< Store the start time of the animation
    int current_step;		///< Current step of animation
    int end_step;			///< A variable storing the step through which the current anim_step will end
    int anim_state;			///< Current Animation State
    uint32_t delay;
} AnimationState_t;

typedef struct {
	uint32_t time;
	uint8_t stateCurrent;
	uint8_t stateCount;
	uint32_t statePrDelay;
	uint32_t statePrEndStep;
	uint8_t is_running;
	uint32_t stateTime[STATE_MAX];
	AnimationState_t state[STATE_MAX];
}argbModule_obj;

/**
 * @brief Enumeration for selecting the RunningLine animation type
 */
typedef enum
{
	RYNNINGLINE_CYCLED,				 ///< The line, when it reaches the end, immediately emerges from the beginning.
	RYNNINGLINE_NORMAL,				 ///< The line goes all the way to the end, with the last pixels smoothly fading away, and after a delay, the line re-emerges from the beginning.
	RYNNINGLINE_DONTERASE,			 ///< The line gradually fills the entire strip (the pixels at the end of the running line are not erased).
	RYNNINGLINE_DONTERASELASTPIXELS  ///< When the beginning of the line reaches the end, the pixels disappear, and after a delay, they move away from the beginning.
} RuningLine_t;

typedef enum
{
	DIRECTION_NORMAL,
	DIRECTION_INVERTED
} Direction_t;

typedef enum
{
	UP,
	DOWN
} RainbowColor_t;

/**
 * @name Control Methods
 * @{
 */

/**
 * @brief Set the time for the Animation object.
 *
 * @param argbModule Pointer to the ARGB module object.
 * @param value The time value to be set.
 * @note  This method will work if the private GetCurrentTime method is set to EXT_TIME.
 */
void ARGB_SetTime(argbModule_obj *argbModule, uint32_t value);

/**
 * @brief End the currently running animation.
 * @param argbModule Pointer to the ARGB module object.
 */
void ARGB_EndAnimation(argbModule_obj *argbModule);
/** @} */


/**
 * @name Animation Methods
 * @{
 */

/**
 * @brief Animation: Blink
 *
 * Initiates and controls a non-blocking blinking animation on a section of an ARGB LED strip.
 * The specified LED segment blinks with a given color for a defined number of times.
 * This function should be called repeatedly in a loop to update the animation state.
 *
 * @param start The starting index of the LEDs to blink (inclusive)
 * @param end The ending index of the LEDs to blink (inclusive).
 * @param r Red component of the color (0-255).
 * @param g Green component of the color (0-255).
 * @param b Blue component of the color (0-255).
 * @param delay_ms The delay in milliseconds between on and off switching of the LEDs.
 * @param BlinkCount The number of times to blink.
 * @param post_delay_ms Delay after animation
 */
void ARGB_Blink(argbModule_obj *argbModule, uint32_t start, uint32_t end, u8_t r, u8_t g, u8_t b, uint32_t delay_ms, uint8_t BlinkCount, uint32_t post_delay_ms);

/**
 * @brief Animation: Running Line
 *
 * Initiates and controls a non-blocking running line animation on a section of an ARGB LED strip.
 * A single LED moves from the start to the end position, creating the effect of a running light.
 * This function should be called repeatedly in a loop to update the animation state.
 *
 * @param start The starting index in the range of LEDs where the effect begins.
 * @param end The ending index in the range of LEDs where the effect ends.
 * @param r The red color component (0-255).
 * @param g The green color component (0-255).
 * @param b The blue color component (0-255).
 * @param delay_ms Delay for animation ticks
 * @param RuningLineType Running line type
 * @param SizieOfLine Running line size
 * @param post_delay_ms Delay after animation
 */
void ARGB_RunningLine(argbModule_obj *argbModule, int start, int end, u8_t r, u8_t g, u8_t b,
					 int delay_ms, RuningLine_t RuningLineType, uint8_t SizieOfLine, uint32_t post_delay_ms, Direction_t direction);
/**
 * @brief Animation: Fade
 *
 * Initiates and controls a non-blocking fade animation on a section of an ARGB LED strip.
 * The LEDs in the specified range fade in and out with the given color.
 * This function should be called repeatedly in a loop to update the animation state.
 *
 * @param start The starting index of the LEDs for the fade effect.
 * @param end The ending index of the LEDs for the fade effect.
 * @param r Red component of the color (0-255).
 * @param g Green component of the color (0-255).
 * @param b Blue component of the color (0-255).
 * @param delay_ms The delay in milliseconds between each step of the fade effect.
 * @param currentBrightness The initial brightness level from which the fading starts and to which it returns. Range is typically from 0 (off) to 255 (maximum brightness).
 */
void ARGB_Fade(argbModule_obj *argbModule, uint32_t start, uint32_t end, u8_t r, u8_t g, u8_t b, uint32_t delay_ms, u8_t currentBrightness);

/**
 * @brief Animation: Color Transition
 *
 * Initiates and controls a non-blocking rainbow color transition animation on a section of an ARGB LED strip.
 * The specified range of hues smoothly transitions across the LED strip, creating a visually appealing color effect.
 * This function should be called repeatedly in a loop to update the animation state.
 *
 * @param startPalette The starting hue value of the color palette (0-255).
 * @param endPalette The ending hue value of the color palette (0-255).
 * @param size The strip buffer size.
 * @param range The level of color sampling within the specified hue range. Small values result in a smoother color transition
 * @param delay_ms The delay in milliseconds between each step of the color transition.
 */
void ARGB_RainbowColor(argbModule_obj *anim, uint32_t startPalete, uint32_t endPalete, RainbowColor_t direction, uint32_t range, uint32_t delay_ms);

/** @} */

/**
 * @name  Private Methods
 * @brief Private methods are a framework for animation methods
 * @{
 */

/**
 * @brief Ends the current animation.
 *
 * This function stops the current animation by setting the is_running flag
 * in the AnimationState structure to false.
 *
 * @param state Pointer to the AnimationState structure holding the animation state.
 */
void _Animation_End(argbModule_obj *anim);

/**
 * @brief Update the animation state.
 *
 * This function should be called on each update cycle to check if it's time to
 * update the animation step. It accounts for the delay and changes the animation
 * step according to the specified change. Returns true if the animation step was updated.
 *
 * @param state Pointer to the AnimationState structure containing the current state of the animation.
 * @param delay_ms The delay in milliseconds between animation step updates.
 * @param step_change The amount by which to change the animation step. Can be positive, negative, or zero.
 * @return Returns true if the animation step was updated, otherwise false.
 */
bool Animation_Update(argbModule_obj *anim, uint32_t animState);

/**
 * @brief Initialize the animation state.
 *
 * This function initializes the animation state by setting initial parameters
 * and marking the start of an animation. It should be called before starting
 * each new animation.
 *
 * @param state Pointer to the AnimationState structure holding the animation state.
 * @param current_step The initial step of the animation.
 * @param end_step The final step of the animation.
 */
void Animation_Init(argbModule_obj *anim, uint32_t delay, uint32_t end_step, uint32_t animState);
/** @} */

/** @} */
#endif

/** @} */ //ARGB Module
