/*
 * CANopen Dynamic Timer Management for STM32 Implementation
 * 
 * @file        CO_timer_STM32.c
 * <AUTHOR> Assistant
 * @date        2024
 * 
 * This file is part of CANopenNode, an opensource CANopen Stack.
 */

#include "CO_timer_STM32.h"
#include "stm32f1xx.h"

/* Forward declaration */
typedef struct CANopenNodeSTM32 CANopenNodeSTM32;

/* Static variables for dynamic timer management */
static uint32_t lastTime_us = 0;
static uint32_t timerNext_us = 1000; // Default 1ms
static bool_t timerInitialized = false;
static uint32_t lastTimeDifference_us = 0;

/* Function to get current time in microseconds */
uint32_t getCurrentTime_us(void)
{
    /* Use DWT (Data Watchpoint and Trace) cycle counter for high precision */
    /* This provides microsecond precision on Cortex-M3/M4 */
    static uint32_t initialized = 0;
    
    if (!initialized) {
        /* Enable DWT */
        CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
        DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
        DWT->CYCCNT = 0;
        initialized = 1;
    }
    
    /* Get system clock frequency */
    uint32_t SystemCoreClock_MHz = SystemCoreClock / 1000000;
    
    /* Convert CPU cycles to microseconds */
    return DWT->CYCCNT / SystemCoreClock_MHz;
}

/* Function to set timer period dynamically with automatic prescaler calculation */
void setTimerPeriod_us(uint32_t period_us)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL) {
        TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

        /* Stop timer */
        HAL_TIM_Base_Stop_IT(htim);

        /* Get timer clock frequency */
        uint32_t timer_clock_hz = SystemCoreClock;

        /* Account for APB prescaler */
        if (htim->Instance == TIM1) {
            timer_clock_hz = HAL_RCC_GetPCLK2Freq();
            if ((RCC->CFGR & RCC_CFGR_PPRE2) != 0) timer_clock_hz *= 2;
        } else {
            timer_clock_hz = HAL_RCC_GetPCLK1Freq();
            if ((RCC->CFGR & RCC_CFGR_PPRE1) != 0) timer_clock_hz *= 2;
        }

        /* Calculate optimal prescaler and period for desired microsecond timing */
        uint32_t prescaler;
        uint32_t period_ticks;

        /* Calculate total ticks needed */
        uint64_t total_ticks = ((uint64_t)timer_clock_hz * period_us) / 1000000;

        /* Find optimal prescaler to fit period in 16-bit ARR register */
        if (total_ticks <= 65535) {
            /* Small period - use minimal prescaler for best resolution */
            prescaler = 1;
            period_ticks = (uint32_t)total_ticks;
        } else {
            /* Large period - calculate optimal prescaler */
            prescaler = (uint32_t)((total_ticks + 65534) / 65535); // Round up
            period_ticks = (uint32_t)(total_ticks / prescaler);
        }

        /* Limit prescaler to 16-bit range */
        if (prescaler > 65535) {
            prescaler = 65535;
            period_ticks = 65535;
        }

        /* Ensure minimum values */
        if (prescaler < 1) prescaler = 1;
        if (period_ticks < 1) period_ticks = 1;
        if (period_ticks > 65535) period_ticks = 65535;

        /* Set new prescaler and period */
        __HAL_TIM_SET_PRESCALER(htim, prescaler - 1);
        __HAL_TIM_SET_AUTORELOAD(htim, period_ticks - 1);
        __HAL_TIM_SET_COUNTER(htim, 0);

        /* Update timer registers */
        htim->Instance->EGR = TIM_EGR_UG; // Generate update event to load new values

        /* Restart timer */
        HAL_TIM_Base_Start_IT(htim);
    }
}

/* Initialize timer management */
void CO_timer_init_STM32(void)
{
    /* Initialize DWT by calling getCurrentTime_us once */
    getCurrentTime_us();

    /* Reset timer variables */
    lastTime_us = 0;
    timerNext_us = 1000;
    timerInitialized = false;
    lastTimeDifference_us = 0;
}

/* Process CANopen interrupt with dynamic timing */
void CO_timer_processInterrupt(bool_t *syncWas, uint32_t *timeDifference_us, uint32_t *timerNext_us_out)
{
    if (syncWas == NULL || timeDifference_us == NULL || timerNext_us_out == NULL) {
        return;
    }

    *syncWas = false;
    uint32_t currentTime_us = getCurrentTime_us();

    /* Initialize timer on first call */
    if (!timerInitialized) {
        lastTime_us = currentTime_us;
        timerInitialized = true;
        *timeDifference_us = 1000; // Default 1ms for first call
    } else {
        /* Calculate real time difference since last function call */
        *timeDifference_us = currentTime_us - lastTime_us;
        /* Prevent overflow and unreasonable values */
        if (*timeDifference_us > 100000) *timeDifference_us = 100000; // Max 100ms
        if (*timeDifference_us < 100) *timeDifference_us = 100;       // Min 100us
    }

    /* Initialize timerNext_us with maximum value */
    timerNext_us = UINT32_MAX;

    /* Update last time and statistics */
    lastTime_us = currentTime_us;
    lastTimeDifference_us = *timeDifference_us;

    /* Return timerNext_us for CANopen functions to modify */
    *timerNext_us_out = timerNext_us;
}

/* Get the next scheduled timer event in microseconds */
uint32_t CO_timer_getNextTimerEvent_us(void)
{
    return timerNext_us;
}

/* Process timer next event after CANopen functions */
void CO_timer_processNextEvent(uint32_t timerNext_us_from_canopen)
{
    /* Set timer for next interrupt based on CANopen scheduling */
    if (timerNext_us_from_canopen == UINT32_MAX || timerNext_us_from_canopen == 0) {
        timerNext_us = 1000; // Default 1ms if no specific timing required
    } else {
        timerNext_us = timerNext_us_from_canopen;
    }

    /* Apply new timer period */
    setTimerPeriod_us(timerNext_us);
}

/* Get timer period range and resolution */
void CO_timer_getRange(CO_timer_range_t *range)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (range != NULL) {
        if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL) {
            TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

            /* Get timer clock frequency */
            uint32_t timer_clock_hz = SystemCoreClock;

            /* Account for APB prescaler */
            if (htim->Instance == TIM1) {
                timer_clock_hz = HAL_RCC_GetPCLK2Freq();
                if ((RCC->CFGR & RCC_CFGR_PPRE2) != 0) timer_clock_hz *= 2;
            } else {
                timer_clock_hz = HAL_RCC_GetPCLK1Freq();
                if ((RCC->CFGR & RCC_CFGR_PPRE1) != 0) timer_clock_hz *= 2;
            }

            /* Calculate range */
            range->timer_clock_hz = timer_clock_hz;
            range->min_period_us = 1000000 / timer_clock_hz;  // 1 tick at prescaler=1
            range->max_period_us = (uint32_t)(((uint64_t)65535 * 65535 * 1000000) / timer_clock_hz); // Max prescaler * Max ARR
            range->resolution_us = range->min_period_us;  // Best resolution with prescaler=1
        } else {
            /* Default values if timer not available */
            range->timer_clock_hz = ********;
            range->min_period_us = 1;
            range->max_period_us = ********; // ~60 seconds
            range->resolution_us = 1;
        }
    }
}

/* Get timing statistics for debugging */
void CO_timer_getTimingStats(CO_TimingStats_t *stats)
{
    if (stats != NULL) {
        stats->lastTimeDifference_us = lastTimeDifference_us;
        stats->nextTimerEvent_us = timerNext_us;
        stats->currentTime_us = getCurrentTime_us();
        stats->timerInitialized = timerInitialized;
    }
}

/* Test function to demonstrate timer range capabilities */
void CO_timer_testRange(void)
{
    CO_timer_range_t range;
    CO_timer_getRange(&range);

    /* Test different periods */
    uint32_t test_periods[] = {
        100,        // 100 us
        1000,       // 1 ms
        10000,      // 10 ms
        100000,     // 100 ms
        1000000,    // 1 second
        5000000,    // 5 seconds
        10000000,   // 10 seconds
        30000000    // 30 seconds
    };

    for (int i = 0; i < 8; i++) {
        uint32_t period = test_periods[i];
        if (period >= range.min_period_us && period <= range.max_period_us) {
            setTimerPeriod_us(period);
            // Small delay to see the effect
            for (volatile int j = 0; j < 1000000; j++);
        }
    }

    /* Return to default 1ms */
    setTimerPeriod_us(1000);
}
