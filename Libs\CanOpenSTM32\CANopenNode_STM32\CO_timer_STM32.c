/**
 * @file 	CO_timer_STM32.c
 * <AUTHOR> Assistant
 * @date 	2024
 * @brief   High-precision timer management for CANopen PDO synchronization
 */

#include "CO_timer_STM32.h"
#include "stm32f1xx.h"

typedef struct CANopenNodeSTM32 CANopenNodeSTM32;

static uint32_t lastTime_us = 0;
static uint32_t timerNext_us = 1000;
static bool_t timerInitialized = false;
static uint32_t lastTimeDifference_us = 0;

uint32_t getCurrentTime_us(void)
{
    static uint32_t initialized = 0;

    if (!initialized)
    {
        CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
        DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
        DWT->CYCCNT = 0;
        initialized = 1;
    }

    uint32_t SystemCoreClock_MHz = SystemCoreClock / 1000000;
    return DWT->CYCCNT / SystemCoreClock_MHz;
}

void setTimerPeriod_us(uint32_t period_us)
{
	/* Получение указателя таймера */
    extern CANopenNodeSTM32* canopenNodeSTM32;
    if (canopenNodeSTM32 == NULL || canopenNodeSTM32->timerHandle == NULL) return;
    TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

    /* Остановка таймера */
    HAL_TIM_Base_Stop_IT(htim);

    /* Определение частоты тактирования таймера */
    uint32_t timer_clock_hz;

    /* TIM1 подключен к APB2, остальные к APB1 */
    if (htim->Instance == TIM1) {
        timer_clock_hz = HAL_RCC_GetPCLK2Freq();
        uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
        if (apb2_prescaler != 0) timer_clock_hz *= 2; // Учёт удвоения частоты
    } else {
        timer_clock_hz = HAL_RCC_GetPCLK1Freq();
        uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
        if (apb1_prescaler != 0) timer_clock_hz *= 2; // Учёт удвоения частоты
    }

    /* Корректировка периода */
    if (period_us == 0) period_us = 1;

    uint32_t prescaler;
    uint32_t period_ticks;
    uint32_t max_32bit_period = UINT32_MAX / timer_clock_hz;

    if (period_us <= max_32bit_period)
    {
        // 32-битная арифметика для периодов < 59 мс.
        uint32_t total_ticks = (timer_clock_hz / 1000) * (period_us / 1000);
        uint32_t remainder_hz = timer_clock_hz % 1000;
        uint32_t remainder_us = period_us % 1000;
        total_ticks += (remainder_hz * remainder_us) / 1000;

        if (total_ticks <= 65535) {
        	prescaler = 1;
        	period_ticks = total_ticks; }
        else {
        	prescaler = (total_ticks + 65534) / 65535;
            period_ticks = total_ticks / prescaler; }
    }
    else
    {
    	// 64-битная арифметика для периодов > 59 мс.
        uint64_t total_ticks_64 = ((uint64_t)timer_clock_hz * (uint64_t)period_us) / 1000000ULL;
        uint64_t max_possible_ticks = (uint64_t)65535 * 65535;

        if (total_ticks_64 > max_possible_ticks) total_ticks_64 = max_possible_ticks;

        prescaler = (uint32_t)((total_ticks_64 + 65534) / 65535);
        period_ticks = (uint32_t)(total_ticks_64 / prescaler);
    }

    /* Проверка границ */
    if (prescaler < 1) prescaler = 1;
    if (prescaler > 65535) prescaler = 65535;
    if (period_ticks < 1) period_ticks = 1;
    if (period_ticks > 65535) period_ticks = 65535;

    /* Применение параметров */
    __HAL_TIM_SET_PRESCALER(htim, prescaler - 1);
    __HAL_TIM_SET_AUTORELOAD(htim, period_ticks - 1);
    __HAL_TIM_SET_COUNTER(htim, 0);

    /* Принудительное обновление регистров */
    htim->Instance->EGR = TIM_EGR_UG;
    __HAL_TIM_CLEAR_FLAG(htim, TIM_FLAG_UPDATE);

    /* Перезапуск таймера */
    HAL_TIM_Base_Start_IT(htim);
}

void CO_timer_init_STM32(void)
{
    getCurrentTime_us();
    lastTime_us = 0;
    timerNext_us = 1000;
    timerInitialized = false;
    lastTimeDifference_us = 0;
}

void CO_timer_processInterrupt(bool_t *syncWas, uint32_t *timeDifference_us, uint32_t *timerNext_us_out)
{
    if (syncWas == NULL || timeDifference_us == NULL || timerNext_us_out == NULL) return;

    *syncWas = false;
    uint32_t currentTime_us = getCurrentTime_us();

    if (!timerInitialized)
    {
        lastTime_us = currentTime_us;
        timerInitialized = true;
        *timeDifference_us = 1000;
    }
    else
    {
        *timeDifference_us = currentTime_us - lastTime_us;
        if (*timeDifference_us > 100000) *timeDifference_us = 100000;
        if (*timeDifference_us < 100) *timeDifference_us = 100;
    }

    timerNext_us = UINT32_MAX;
    lastTime_us = currentTime_us;
    lastTimeDifference_us = *timeDifference_us;
    *timerNext_us_out = timerNext_us;
}

uint32_t CO_timer_getNextTimerEvent_us(void)
{
    return timerNext_us;
}

void CO_timer_processNextEvent(uint32_t timerNext_us_from_canopen)
{
    if (timerNext_us_from_canopen == UINT32_MAX || timerNext_us_from_canopen == 0)
    {
        timerNext_us = 1000;
    }
    else
    {
        timerNext_us = timerNext_us_from_canopen;
    }

    setTimerPeriod_us(timerNext_us);
}

void CO_timer_getRange(CO_timer_range_t *range)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (range == NULL) return;

    if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL)
    {
        TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;
        uint32_t timer_clock_hz;

        if (htim->Instance == TIM1)
        {
            timer_clock_hz = HAL_RCC_GetPCLK2Freq();
            uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
            if (apb2_prescaler != 0) timer_clock_hz *= 2;
        }
        else
        {
            timer_clock_hz = HAL_RCC_GetPCLK1Freq();
            uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
            if (apb1_prescaler != 0) timer_clock_hz *= 2;
        }

        range->timer_clock_hz = timer_clock_hz;
        range->min_period_us = (1000000 + timer_clock_hz - 1) / timer_clock_hz;
        range->max_period_us = (uint32_t)(((uint64_t)65535 * 65535 * 1000000) / timer_clock_hz);
        range->resolution_us = range->min_period_us;
    }
    else
    {
        range->timer_clock_hz = 72000000;
        range->min_period_us = 1;
        range->max_period_us = 60000000;
        range->resolution_us = 1;
    }
}

void CO_timer_getTimingStats(CO_TimingStats_t *stats)
{
    if (stats != NULL)
    {
        stats->lastTimeDifference_us = lastTimeDifference_us;
        stats->nextTimerEvent_us = timerNext_us;
        stats->currentTime_us = getCurrentTime_us();
        stats->timerInitialized = timerInitialized;
    }
}
