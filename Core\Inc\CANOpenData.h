#ifndef INC_CANOPENDATA_H_
#define INC_CANOPENDATA_H_

#include "CANopen.h"
#include "OD.h"

/**
 * @addtogroup CanOpenData
 * @brief      This module contains a structure
 *             for easy interaction with data in OD
 * @{
 */

/// @brief OD Data structure
typedef struct
{
	uint32_t Timer;
	uint8_t Mode;
	uint8_t ModeParams;
	uint8_t ModeParamsArray[OD_CNT_ARR_6003];
	uint8_t LifterStatus;
} CANOpenData_t;

// OD
#define OD_TIMER	 	  		OD_RAM.x6000_timer
#define OD_MODE 		  		OD_RAM.x6001_mode
#define OD_MODE_PARAMS			OD_RAM.x6002_modeParams
#define OD_MODE_PARAMS_ARRAY	OD_RAM.x6003_modeParamsArray
#define OD_LIFTER_STATUS		OD_RAM.x6004_lifterStatus

/// @brief The macro fills CANOpenData_t with values from OD
#define OD_DATA() (CANOpenData_t) { \
	.Timer = OD_TIMER, \
    .Mode = OD_MODE, \
    .ModeParams = OD_MODE_PARAMS, \
	.ModeParamsArray = INIT_MODE_PARAMS_ARRAY, \
	.LifterStatus = GET_FIRST_THREE_BITS(OD_LIFTER_STATUS) \
}

typedef enum
{
	DISTANCE_CLOSE,
	DISTANCE_MEDIUM,
	DISTANCE_FAR,
} Distance_t;

typedef enum
{
	SCALE_LIGHT,
	SCALE_MEDIUM,
	SCALE_HEAVY,
} Scale_t;

typedef enum
{
	EMERGENCY_NO,
	EMERGENCY_OBSTACLE_DETECTED,
	EMERGENCY_MALFUNCTION,
} Emergency_t;

typedef enum
{
	MOVEMENT_STATE_STAND,
	MOVEMENT_STATE_FORWARD,
	MOVEMENT_STATE_LEFT,
	MOVEMENT_STATE_RIGHT,
	MOVEMENT_STATE_BACK,
} MovementState_t;


/// @cond

// TPDO ID
#define TPDO_1 0
#define TPDO_2 1
#define TPDO_3 2
#define TPDO_4 3
#define TPDO_5 4

// TPDO MAPPING
#define TPDO_SYNCMessage  TPDO_1

#define GET_FIRST_THREE_BITS(n) ((n) & 0b111)

#define INIT_MODE_PARAMS_ARRAY { \
	OD_MODE_PARAMS_ARRAY[0], \
	OD_MODE_PARAMS_ARRAY[1], \
	OD_MODE_PARAMS_ARRAY[2], \
	OD_MODE_PARAMS_ARRAY[3], \
	OD_MODE_PARAMS_ARRAY[4]}

/// @endcond

/** @} */

#endif
