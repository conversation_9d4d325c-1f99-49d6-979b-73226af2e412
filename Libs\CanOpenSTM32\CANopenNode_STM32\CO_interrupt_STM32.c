#include "CO_interrupt_STM32.h"

static SemaphoreHandle_t CO_Interrupt_Mutex = NULL;

uint32_t getCurrentTime_us(void)
{
    static uint32_t initialized = 0;
    
    if (!initialized) 
    {
        CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
        DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
        DWT->CYCCNT = 0;
        initialized = 1;
    }
    
    uint32_t SystemCoreClock_MHz = SystemCoreClock / 1000000;
    return DWT->CYCCNT / SystemCoreClock_MHz;
}

void setTimerPeriod_us(uint32_t period_us)
{
	/* Получение указателя таймера */
    extern CANopenNodeSTM32* canopenNodeSTM32;
    if (canopenNodeSTM32 == NULL || canopenNodeSTM32->timerHandle == NULL) return;
    TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;
    
    /* Остановка таймера */
    HAL_TIM_Base_Stop_IT(htim);
    
    /* Определение частоты тактирования таймера */
    uint32_t timer_clock_hz;
    if (htim->Instance == TIM1) {
        timer_clock_hz = HAL_RCC_GetPCLK2Freq();
        uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
        if (apb2_prescaler != 0) timer_clock_hz *= 2;
    }
    else {
        timer_clock_hz = HAL_RCC_GetPCLK1Freq();
        uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
        if (apb1_prescaler != 0) timer_clock_hz *= 2;
    }

    /* Корректировка периода */
    if (period_us == 0) period_us = 1;
    
    uint32_t prescaler;
    uint32_t period_ticks;
    uint32_t max_32bit_period = UINT32_MAX / timer_clock_hz;
    
    if (period_us <= max_32bit_period)
    {
    	/* 32-битная арифметика для периодов до ~59 мс */
        uint32_t total_ticks = (timer_clock_hz / 1000) * (period_us / 1000);
        uint32_t remainder_hz = timer_clock_hz % 1000;
        uint32_t remainder_us = period_us % 1000;
        total_ticks += (remainder_hz * remainder_us) / 1000;
        
        if (total_ticks <= 65535) {
            prescaler = 1;
            period_ticks = total_ticks; }
        else {
            prescaler = (total_ticks + 65534) / 65535;
            period_ticks = total_ticks / prescaler; }
    } 
    else 
    {
        /* 64-битная арифметика для больших периодов */
        uint64_t total_ticks_64 = ((uint64_t)timer_clock_hz * (uint64_t)period_us) / 1000000ULL;
        uint64_t max_possible_ticks = (uint64_t)65535 * 65535;
        
        if (total_ticks_64 > max_possible_ticks) total_ticks_64 = max_possible_ticks;
        
        prescaler = (uint32_t)((total_ticks_64 + 65534) / 65535);
        period_ticks = (uint32_t)(total_ticks_64 / prescaler);
    }

    /* Проверка границ */
    if (prescaler < 1) prescaler = 1;
    if (prescaler > 65535) prescaler = 65535;
    if (period_ticks < 1) period_ticks = 1;
    if (period_ticks > 65535) period_ticks = 65535;

    /* Применение параметров */
    __HAL_TIM_SET_PRESCALER(htim, prescaler - 1);
    __HAL_TIM_SET_AUTORELOAD(htim, period_ticks - 1);
    __HAL_TIM_SET_COUNTER(htim, 0);

    /* Принудительное обновление регистров */
    htim->Instance->EGR = TIM_EGR_UG;
    __HAL_TIM_CLEAR_FLAG(htim, TIM_FLAG_UPDATE);

    /* Перезапуск таймера */
    HAL_TIM_Base_Start_IT(htim);
}

/* Interrupt synchronization functions */

void CO_interrupt_init(void)
{
    CO_Interrupt_Mutex = xSemaphoreCreateBinary();
}

bool CO_interrupt_take(void)
{
    if (CO_Interrupt_Mutex == NULL) return false;
    return (xSemaphoreTake(CO_Interrupt_Mutex, portMAX_DELAY) == pdTRUE);
}

void CO_interrupt_giveFromISR(void)
{
    if (CO_Interrupt_Mutex != NULL)
    {
        BaseType_t xHigherPriorityTaskWoken = pdFALSE;
        xSemaphoreGiveFromISR(CO_Interrupt_Mutex, &xHigherPriorityTaskWoken);
        HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);
        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
    }
}
