/*
 * CANopen Dynamic Timer Management for STM32
 * 
 * @file        CO_timer_STM32.h
 * <AUTHOR> Assistant
 * @date        2024
 * 
 * This file is part of CANopenNode, an opensource CANopen Stack.
 */

#ifndef CO_TIMER_STM32_H
#define CO_TIMER_STM32_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"
#include "CANopen.h"

/* Function to get current time in microseconds */
uint32_t getCurrentTime_us(void);

/* Function to set timer period dynamically */
void setTimerPeriod_us(uint32_t period_us);

/* Initialize timer management */
void CO_timer_init_STM32(void);

#ifdef __cplusplus
}
#endif

#endif /* CO_TIMER_STM32_H */
