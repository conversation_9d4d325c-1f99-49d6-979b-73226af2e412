/*
 * Модуль динамического управления таймером для CANopen STM32
 *
 * @file        CO_timer_STM32.h
 * <AUTHOR> Assistant
 * @date        2024
 *
 * Данный модуль обеспечивает высокоточное управление таймером для синхронизации
 * CANopen PDO с автоматическим расчетом предделителя и поддержкой диапазона
 * от 1 микросекунды до 60 секунд.
 */

#ifndef CO_TIMER_STM32_H
#define CO_TIMER_STM32_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"
#include "CANopen.h"

/* Структура статистики таймера */
typedef struct {
    uint32_t lastTimeDifference_us;     // Последняя разница времени в мкс
    uint32_t nextTimerEvent_us;         // Время до следующего события в мкс
    uint32_t currentTime_us;            // Текущее время в мкс
    bool_t timerInitialized;            // Флаг инициализации таймера
} CO_TimingStats_t;

/* Структура диапазона таймера */
typedef struct {
    uint32_t min_period_us;             // Минимальный период в мкс
    uint32_t max_period_us;             // Максимальный период в мкс
    uint32_t resolution_us;             // Лучшее разрешение в мкс
    uint32_t timer_clock_hz;            // Частота таймера в Гц
} CO_timer_range_t;

/* Получить текущее время в микросекундах */
uint32_t getCurrentTime_us(void);

/* Установить период таймера с автоматическим расчетом предделителя */
void setTimerPeriod_us(uint32_t period_us);

/* Инициализация модуля управления таймером */
void CO_timer_init_STM32(void);

/* Обработка прерывания CANopen с динамическим тайминг */
void CO_timer_processInterrupt(bool_t *syncWas, uint32_t *timeDifference_us, uint32_t *timerNext_us);

/* Получить время до следующего запланированного события */
uint32_t CO_timer_getNextTimerEvent_us(void);

/* Обработать следующее событие таймера после функций CANopen */
void CO_timer_processNextEvent(uint32_t timerNext_us_from_canopen);

/* Получить диапазон и разрешение периода таймера */
void CO_timer_getRange(CO_timer_range_t *range);

/* Получить статистику тайминг для отладки */
void CO_timer_getTimingStats(CO_TimingStats_t *stats);

#ifdef __cplusplus
}
#endif

#endif /* CO_TIMER_STM32_H */
