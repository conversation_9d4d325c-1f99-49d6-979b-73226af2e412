/*
 * Device and application specific definitions for CANopenNode.
 *
 * @file        CO_driver_target.h
 * <AUTHOR> 	2022
 * 				Tilen Marjerle		2021
 * 				<PERSON><PERSON> Paternoster	2020
 * @copyright   2004 - 2020 <PERSON><PERSON> Paternoster
 *
 * This file is part of CANopenNode, an opensource CANopen Stack.
 * Project home page is <https://github.com/CANopenNode/CANopenNode>.
 * For more information on CANopen see <http://www.can-cia.org/>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef CO_DRIVER_TARGET_H
#define CO_DRIVER_TARGET_H

/* This file contains device and application specific definitions.
 * It is included from CO_driver.h, which contains documentation
 * for common definitions below. */

#include "main.h"
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include "cmsis_os.h"
#include "FreeRTOS.h"

// Determining the CANOpen Driver

#if defined(FDCAN) || defined(FDCAN1) || defined(FDCAN2) || defined(FDCAN3)
#define CO_STM32_FDCAN_Driver 1
#elif defined(CAN) || defined(CAN1) || defined(CAN2) || defined(CAN3)
#define CO_STM32_CAN_Driver 1
#else
#error This STM32 Do not support CAN or FDCAN
#endif

#ifdef CO_DRIVER_CUSTOM
#include "CO_driver_custom.h"
#else

//#undef CO_CONFIG_STORAGE_ENABLE // We don't need Storage option, implement based on your use case and remove this line from here
#define CO_CONFIG_STORAGE (CO_CONFIG_STORAGE_ENABLE)
// Config PDO
#define CO_CONFIG_PDO ( CO_CONFIG_FLAG_CALLBACK_PRE | CO_CONFIG_RPDO_ENABLE | CO_CONFIG_RPDO_ENABLE | CO_CONFIG_TPDO_ENABLE | CO_CONFIG_TPDO_TIMERS_ENABLE |CO_CONFIG_FLAG_OD_DYNAMIC |CO_CONFIG_PDO_OD_IO_ACCESS)

#define CO_CONFIG_SDO_CLI (CO_CONFIG_SDO_CLI_ENABLE)


#ifndef CO_CONFIG_FIFO
#define CO_CONFIG_FIFO (CO_CONFIG_FIFO_ENABLE | \
                        CO_CONFIG_FIFO_ALT_READ | \
                        CO_CONFIG_FIFO_CRC16_CCITT )
#endif

#ifndef CO_CONFIG_GFC
#define CO_CONFIG_GFC (0)
#endif

#ifndef CO_CONFIG_SRDO
#define CO_CONFIG_SRDO (0)
#endif

#ifndef CO_CONFIG_SRDO_MINIMUM_DELAY
#define CO_CONFIG_SRDO_MINIMUM_DELAY 0
#endif

#ifndef CO_CONFIG_LSS
#define CO_CONFIG_LSS (0)
#endif


#ifndef CO_CONFIG_GTW
#define CO_CONFIG_GTW (0)
#endif

#ifndef CO_CONFIG_CRC16
#define CO_CONFIG_CRC16 (CO_CONFIG_CRC16_ENABLE)
#endif

#ifndef CO_CONFIG_TRACE
//#define CO_CONFIG_TRACE (CO_CONFIG_TRACE_ENABLE)
#define CO_CONFIG_TRACE (0)
#endif

#define CO_CONFIG_NMT (0)

#ifndef CO_CONFIG_LEDS
#define CO_CONFIG_LEDS (0)
#endif

#define CO_CONFIG_HB_CONS (CO_CONFIG_HB_CONS_ENABLE | \
                           CO_CONFIG_GLOBAL_FLAG_TIMERNEXT | \
						   CO_CONFIG_HB_CONS_QUERY_FUNCT)

#define CO_CONFIG_SDO_SRV (CO_CONFIG_GLOBAL_FLAG_CALLBACK_PRE | \
                           CO_CONFIG_GLOBAL_FLAG_TIMERNEXT | \
                           CO_CONFIG_GLOBAL_FLAG_OD_DYNAMIC)

#define CO_CONFIG_TIME (0)

#define CO_CONFIG_EM (0)

#define CO_CONFIG_SYNC (0)

#endif

#ifdef __cplusplus
extern "C" {
#endif

/* Stack configuration override default values.
 * For more information see file CO_config.h. */

/* Basic definitions. If big endian, CO_SWAP_xx macros must swap bytes. */
#define CO_LITTLE_ENDIAN
#define CO_SWAP_16(x) x
#define CO_SWAP_32(x) x
#define CO_SWAP_64(x) x

/* NULL is defined in stddef.h */
/* true and false are defined in stdbool.h */
/* int8_t to uint64_t are defined in stdint.h */
typedef uint_fast8_t bool_t;
typedef float float32_t;
typedef double float64_t;

/**
 * \brief           CAN RX message for platform
 *
 * This is platform specific one
 */
typedef struct {
    uint32_t ident;  /*!< Standard identifier */
    uint8_t dlc;     /*!< Data length */
    uint8_t data[8]; /*!< Received data */
} CO_CANrxMsg_t;

/* Access to received CAN message */
#define CO_CANrxMsg_readIdent(msg) ((uint16_t)(((CO_CANrxMsg_t*)(msg)))->ident)
#define CO_CANrxMsg_readDLC(msg)   ((uint8_t)(((CO_CANrxMsg_t*)(msg)))->dlc)
#define CO_CANrxMsg_readData(msg)  ((uint8_t*)(((CO_CANrxMsg_t*)(msg)))->data)

/* Received message object */
typedef struct {
    uint16_t ident;
    uint16_t mask;
    uint16_t identCan;
    uint16_t maskCan;
    void* object;
    void (*CANrx_callback)(void* object, void* message);
} CO_CANrx_t;

/* Transmit message object */
typedef struct {
    uint32_t ident;
    uint8_t DLC;
    uint8_t data[8];
    volatile bool_t bufferFull;
    volatile bool_t syncFlag;
} CO_CANtx_t;

/* CAN module object */
typedef struct {
    void* CANptr;
    CO_CANrx_t* rxArray;
    uint16_t rxSize;
    CO_CANtx_t* txArray;
    uint16_t txSize;
    uint16_t CANerrorStatus;
    volatile bool_t CANnormal;
    volatile bool_t useCANrxFilters;
    volatile bool_t bufferInhibitFlag;
    volatile bool_t firstCANtxMessage;
    volatile uint16_t CANtxCount;
    uint32_t errOld;

    /* STM32 specific features */
    uint32_t primask_send; /* Primask register for interrupts for send operation */
    uint32_t primask_emcy; /* Primask register for interrupts for emergency operation */
    uint32_t primask_od;   /* Primask register for interrupts for send operation */

} CO_CANmodule_t;

/* Data storage object for one entry */
typedef struct {
    void* addr;
    size_t len;
    uint8_t subIndexOD;
    uint8_t attr;
    void* storageModule;
    uint32_t signature;
    size_t eepromAddr;
    uint16_t crc;
    size_t eepromAddrSignature;
    size_t offset;
    /* Additional variables (target specific) */
    void* addrNV;
} CO_storage_entry_t;


/* (un)lock critical section in CO_CANsend() */
extern xSemaphoreHandle CANsend_Mutex;
#define CO_LOCK_CAN_SEND(CAN_MODULE)	xSemaphoreTake(CANsend_Mutex,portMAX_DELAY);
#define CO_UNLOCK_CAN_SEND(CAN_MODULE)	xSemaphoreGive(CANsend_Mutex);

/* (un)lock critical section in CO_errorReport() or CO_errorReset() */
extern xSemaphoreHandle EMCY_Mutex;
#define CO_LOCK_EMCY(CAN_MODULE)		xSemaphoreTake(EMCY_Mutex,portMAX_DELAY);
#define CO_UNLOCK_EMCY(CAN_MODULE)		xSemaphoreGive(EMCY_Mutex);

/* (un)lock critical section when accessing Object Dictionary */
extern xSemaphoreHandle OD_Mutex;
#define CO_LOCK_OD(CAN_MODULE)			xSemaphoreTake(OD_Mutex,portMAX_DELAY);
#define CO_UNLOCK_OD(CAN_MODULE)		xSemaphoreGive(OD_Mutex);


/* Synchronization between CAN receive and message processing threads. */
#define CO_MemoryBarrier()
#define CO_FLAG_READ(rxNew) ((rxNew) != NULL)
#define CO_FLAG_SET(rxNew)                                                                                             \
    do {                                                                                                               \
        CO_MemoryBarrier();                                                                                            \
        rxNew = (void*)1L;                                                                                             \
    } while (0)
#define CO_FLAG_CLEAR(rxNew)                                                                                           \
    do {                                                                                                               \
        CO_MemoryBarrier();                                                                                            \
        rxNew = NULL;                                                                                                  \
    } while (0)


#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* CO_DRIVER_TARGET_H */
