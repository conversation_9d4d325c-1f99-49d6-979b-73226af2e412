#include "CO_interrupt_STM32.h"

static SemaphoreHandle_t CO_Interrupt_Mutex = NULL;

uint32_t getCurrentTime_us(void)
{
    static uint32_t initialized = 0;
    
    if (!initialized) 
    {
        CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
        DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
        DWT->CYCCNT = 0;
        initialized = 1;
    }
    
    uint32_t SystemCoreClock_MHz = SystemCoreClock / 1000000;
    return DWT->CYCCNT / SystemCoreClock_MHz;
}

void setTimerPeriod_us(uint32_t period_us)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (canopenNodeSTM32 == NULL || canopenNodeSTM32->timerHandle == NULL) return;

    TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

    // Bounds checking for period (max 65535 us in microsecond mode)
    if (period_us > 65535) period_us = 65535;
    if (period_us < 1) period_us = 1;

    // Get current counter value for relative timing
    uint32_t current_counter = __HAL_TIM_GET_COUNTER(htim);
    uint32_t next_compare = current_counter + period_us;

    // Handle counter overflow (ARR = 65535)
    if (next_compare > 65535)
    {
        next_compare = next_compare - 65536;  // Wrap around
    }

    // Set Output Compare value for next interrupt
    __HAL_TIM_SET_COMPARE(htim, TIM_CHANNEL_1, next_compare);
}

int setTimPrescaller(TIM_HandleTypeDef *htim, uint16_t autoreload, uint32_t time)
{
	int status = 0;

	register uint32_t total_tick = HAL_RCC_GetHCLKFreq() * time;
	register uint32_t prescaler = ((total_tick) / autoreload);

	if (prescaler > UINT16_MAX) { prescaler = UINT16_MAX; status = -1; }
	else if (prescaler < 2) 	{ prescaler = 2; status = -1; }
	if (autoreload < 2) 		{ autoreload = 2; status = -1; }

	prescaler &= 0xFFFF;
    prescaler--;
    autoreload--;

    __HAL_TIM_SET_AUTORELOAD(htim, autoreload);
    __HAL_TIM_SET_PRESCALER(htim, prescaler);

    return status;
}

/* Interrupt synchronization functions */

void CO_interrupt_init(void)
{
    CO_Interrupt_Mutex = xSemaphoreCreateBinary();
}

bool CO_interrupt_take(void)
{
    if (CO_Interrupt_Mutex == NULL) return false;
    return (xSemaphoreTake(CO_Interrupt_Mutex, portMAX_DELAY) == pdTRUE);
}

void CO_interrupt_giveFromISR(void)
{
    if (CO_Interrupt_Mutex != NULL)
    {
        BaseType_t xHigherPriorityTaskWoken = pdFALSE;
        xSemaphoreGiveFromISR(CO_Interrupt_Mutex, &xHigherPriorityTaskWoken);
        HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);
        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
    }
}
