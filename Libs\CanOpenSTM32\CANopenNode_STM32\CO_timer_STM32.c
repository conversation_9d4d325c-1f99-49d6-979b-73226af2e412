/*
 * Модуль динамического управления таймером для CANopen STM32
 *
 * @file        CO_timer_STM32.c
 * <AUTHOR> Assistant
 * @date        2024
 *
 * Реализация высокоточного управления таймером с автоматическим расчетом
 * предделителя для оптимальной производительности и точности.
 */

#include "CO_timer_STM32.h"
#include "stm32f1xx.h"

/* Предварительное объявление структуры */
typedef struct CANopenNodeSTM32 CANopenNodeSTM32;

/* Статические переменные для управления динамическим таймером */
static uint32_t lastTime_us = 0;                // Время последнего вызова
static uint32_t timerNext_us = 1000;            // Время следующего события (по умолчанию 1мс)
static bool_t timerInitialized = false;         // Флаг инициализации таймера
static uint32_t lastTimeDifference_us = 0;      // Последняя разница времени

/*
 * Получить текущее время в микросекундах
 * Использует DWT (Data Watchpoint and Trace) для высокой точности
 */
uint32_t getCurrentTime_us(void)
{
    static uint32_t initialized = 0;

    if (!initialized) {
        // Включить DWT для высокоточного счета циклов
        CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
        DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
        DWT->CYCCNT = 0;
        initialized = 1;
    }

    // Получить частоту системы в МГц
    uint32_t SystemCoreClock_MHz = SystemCoreClock / 1000000;

    // Преобразовать циклы CPU в микросекунды
    return DWT->CYCCNT / SystemCoreClock_MHz;
}

/*
 * Установить период таймера с автоматическим расчетом предделителя
 * Поддерживает диапазон от 1 мкс до 60 секунд с оптимизацией производительности
 */
void setTimerPeriod_us(uint32_t period_us)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (canopenNodeSTM32 == NULL || canopenNodeSTM32->timerHandle == NULL) {
        return;
    }

    TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

    // Остановить таймер перед изменением настроек
    HAL_TIM_Base_Stop_IT(htim);

    // Получить частоту таймера с корректной обработкой предделителей APB
    uint32_t timer_clock_hz;

    if (htim->Instance == TIM1) {
        // TIM1 находится на шине APB2
        timer_clock_hz = HAL_RCC_GetPCLK2Freq();
        // Если предделитель APB2 != 1, частота таймера удваивается
        uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
        if (apb2_prescaler != 0) {  // 0 означает отсутствие деления (предделитель = 1)
            timer_clock_hz *= 2;
        }
    } else {
        // Остальные таймеры находятся на шине APB1
        timer_clock_hz = HAL_RCC_GetPCLK1Freq();
        // Если предделитель APB1 != 1, частота таймера удваивается
        uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
        if (apb1_prescaler != 0) {  // 0 означает отсутствие деления (предделитель = 1)
            timer_clock_hz *= 2;
        }
    }

        /* Validate input period */
        if (period_us == 0) {
            period_us = 1; /* Minimum 1 microsecond */
        }

    // Проверить входной параметр
    if (period_us == 0) {
        period_us = 1; // Минимум 1 микросекунда
    }

    uint32_t prescaler;
    uint32_t period_ticks;

    // Быстрый путь для малых периодов с использованием 32-битной арифметики
    uint32_t max_32bit_period = UINT32_MAX / timer_clock_hz;  // ~59мс при 72МГц

    if (period_us <= max_32bit_period) {
        // Быстрый 32-битный расчет для периодов до ~59мс
        uint32_t total_ticks = (timer_clock_hz / 1000) * (period_us / 1000);

        // Обработка остатков для лучшей точности
        uint32_t remainder_hz = timer_clock_hz % 1000;
        uint32_t remainder_us = period_us % 1000;
        total_ticks += (remainder_hz * remainder_us) / 1000;

        if (total_ticks <= 65535) {
            // Малый период - использовать предделитель = 1 для лучшего разрешения
            prescaler = 1;
            period_ticks = total_ticks;
        } else {
            // Средний период - рассчитать оптимальный предделитель
            prescaler = (total_ticks + 65534) / 65535; // Деление с округлением вверх
            period_ticks = total_ticks / prescaler;
        }
    } else {
        // Медленный путь для больших периодов с использованием 64-битной арифметики
        uint64_t total_ticks_64 = ((uint64_t)timer_clock_hz * (uint64_t)period_us) / 1000000ULL;

        // Проверить, не слишком ли большой период
        uint64_t max_possible_ticks = (uint64_t)65535 * 65535; // Макс предделитель * Макс ARR
        if (total_ticks_64 > max_possible_ticks) {
            // Период слишком большой - ограничить максимумом
            total_ticks_64 = max_possible_ticks;
        }

        // Большой период - рассчитать минимально необходимый предделитель
        prescaler = (uint32_t)((total_ticks_64 + 65534) / 65535); // Деление с округлением вверх

        // Пересчитать period_ticks с точным предделителем
        period_ticks = (uint32_t)(total_ticks_64 / prescaler);
    }

    // Финальная проверка границ
    if (prescaler < 1) prescaler = 1;
    if (prescaler > 65535) prescaler = 65535;
    if (period_ticks < 1) period_ticks = 1;
    if (period_ticks > 65535) period_ticks = 65535;

    // Установить новый предделитель и период (регистры ожидают значения на основе 0)
    __HAL_TIM_SET_PRESCALER(htim, prescaler - 1);
    __HAL_TIM_SET_AUTORELOAD(htim, period_ticks - 1);
    __HAL_TIM_SET_COUNTER(htim, 0);

    // Сгенерировать событие обновления для немедленной загрузки новых значений PSC и ARR
    htim->Instance->EGR = TIM_EGR_UG;

    // Очистить флаг прерывания обновления, который был установлен UG
    __HAL_TIM_CLEAR_FLAG(htim, TIM_FLAG_UPDATE);

    // Перезапустить таймер
    HAL_TIM_Base_Start_IT(htim);
}

/*
 * Инициализация модуля управления таймером
 * Сбрасывает все переменные и инициализирует DWT для высокоточного времени
 */
void CO_timer_init_STM32(void)
{
    // Инициализировать DWT вызовом getCurrentTime_us
    getCurrentTime_us();

    // Сбросить переменные таймера
    lastTime_us = 0;
    timerNext_us = 1000;                // По умолчанию 1мс
    timerInitialized = false;
    lastTimeDifference_us = 0;
}

/*
 * Обработка прерывания CANopen с динамическим тайминг
 * Рассчитывает реальную разность времени и подготавливает переменные для CANopen функций
 */
void CO_timer_processInterrupt(bool_t *syncWas, uint32_t *timeDifference_us, uint32_t *timerNext_us_out)
{
    if (syncWas == NULL || timeDifference_us == NULL || timerNext_us_out == NULL) {
        return;
    }

    *syncWas = false;
    uint32_t currentTime_us = getCurrentTime_us();

    // Инициализация таймера при первом вызове
    if (!timerInitialized) {
        lastTime_us = currentTime_us;
        timerInitialized = true;
        *timeDifference_us = 1000; // По умолчанию 1мс для первого вызова
    } else {
        // Рассчитать реальную разность времени с последнего вызова функции
        *timeDifference_us = currentTime_us - lastTime_us;
        // Предотвратить переполнение и неразумные значения
        if (*timeDifference_us > 100000) *timeDifference_us = 100000; // Макс 100мс
        if (*timeDifference_us < 100) *timeDifference_us = 100;       // Мин 100мкс
    }

    // Инициализировать timerNext_us максимальным значением
    timerNext_us = UINT32_MAX;

    // Обновить последнее время и статистику
    lastTime_us = currentTime_us;
    lastTimeDifference_us = *timeDifference_us;

    // Вернуть timerNext_us для изменения функциями CANopen
    *timerNext_us_out = timerNext_us;
}

/*
 * Получить время до следующего запланированного события
 */
uint32_t CO_timer_getNextTimerEvent_us(void)
{
    return timerNext_us;
}

/*
 * Обработать следующее событие таймера после функций CANopen
 * Устанавливает новый период таймера на основе расписания CANopen
 */
void CO_timer_processNextEvent(uint32_t timerNext_us_from_canopen)
{
    // Установить таймер для следующего прерывания на основе расписания CANopen
    if (timerNext_us_from_canopen == UINT32_MAX || timerNext_us_from_canopen == 0) {
        timerNext_us = 1000; // По умолчанию 1мс, если нет специфических требований к времени
    } else {
        timerNext_us = timerNext_us_from_canopen;
    }

    // Применить новый период таймера
    setTimerPeriod_us(timerNext_us);
}

/*
 * Получить диапазон и разрешение периода таймера
 * Рассчитывает минимальный и максимальный поддерживаемые периоды
 */
void CO_timer_getRange(CO_timer_range_t *range)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (range == NULL) {
        return;
    }

    if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL) {
        TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

        // Получить частоту таймера с корректной обработкой предделителей APB
        uint32_t timer_clock_hz;

        if (htim->Instance == TIM1) {
            // TIM1 находится на шине APB2
            timer_clock_hz = HAL_RCC_GetPCLK2Freq();
            // Если предделитель APB2 != 1, частота таймера удваивается
            uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
            if (apb2_prescaler != 0) {
                timer_clock_hz *= 2;
            }
        } else {
            // Остальные таймеры находятся на шине APB1
            timer_clock_hz = HAL_RCC_GetPCLK1Freq();
            // Если предделитель APB1 != 1, частота таймера удваивается
            uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
            if (apb1_prescaler != 0) {
                timer_clock_hz *= 2;
            }
        }

        // Рассчитать диапазон
        range->timer_clock_hz = timer_clock_hz;
        range->min_period_us = (1000000 + timer_clock_hz - 1) / timer_clock_hz;  // 1 тик при предделителе=1, округление вверх
        range->max_period_us = (uint32_t)(((uint64_t)65535 * 65535 * 1000000) / timer_clock_hz); // Макс предделитель * Макс ARR
        range->resolution_us = range->min_period_us;  // Лучшее разрешение при предделителе=1
    } else {
        // Значения по умолчанию, если таймер недоступен
        range->timer_clock_hz = 72000000;
        range->min_period_us = 1;
        range->max_period_us = 60000000; // ~60 секунд
        range->resolution_us = 1;
    }
}

/* Get timing statistics for debugging */
void CO_timer_getTimingStats(CO_TimingStats_t *stats)
{
    if (stats != NULL) {
        stats->lastTimeDifference_us = lastTimeDifference_us;
        stats->nextTimerEvent_us = timerNext_us;
        stats->currentTime_us = getCurrentTime_us();
        stats->timerInitialized = timerInitialized;
    }
}

/*
 * Получить статистику тайминг для отладки
 * Возвращает текущие значения переменных таймера
 */
void CO_timer_getTimingStats(CO_TimingStats_t *stats)
{
    if (stats != NULL) {
        stats->lastTimeDifference_us = lastTimeDifference_us;
        stats->nextTimerEvent_us = timerNext_us;
        stats->currentTime_us = getCurrentTime_us();
        stats->timerInitialized = timerInitialized;
    }
}
