/*
 * CANopen Dynamic Timer Test Functions
 * 
 * This file contains test functions to demonstrate and verify
 * the dynamic timer functionality for CANopen PDO synchronization.
 * 
 * @file        canopen_timer_test.c
 * <AUTHOR> Assistant
 * @date        2024
 */

#include "canopen_timer_test.h"
#include "CO_app_STM32.h"
#include <stdio.h>

/* Test variables */
static uint32_t test_counter = 0;
static CO_TimingStats_t last_stats = {0};

/* Test function to print timing statistics */
void canopen_timer_test_printStats(void)
{
    CO_TimingStats_t stats;
    canopen_app_getTimingStats(&stats);
    
    printf("=== CANopen Timer Statistics ===\n");
    printf("Timer Initialized: %s\n", stats.timerInitialized ? "YES" : "NO");
    printf("Current Time: %lu us\n", stats.currentTime_us);
    printf("Last Time Difference: %lu us\n", stats.lastTimeDifference_us);
    printf("Next Timer Event: %lu us\n", stats.nextTimerEvent_us);
    printf("Test Counter: %lu\n", test_counter++);
    printf("================================\n\n");
    
    last_stats = stats;
}

/* Test function to verify timer frequency */
void canopen_timer_test_verifyFrequency(void)
{
    static uint32_t last_time = 0;
    static uint32_t call_count = 0;
    static uint32_t total_time_diff = 0;
    
    CO_TimingStats_t stats;
    canopen_app_getTimingStats(&stats);
    
    if (last_time != 0) {
        uint32_t time_diff = stats.currentTime_us - last_time;
        total_time_diff += time_diff;
        call_count++;
        
        if (call_count >= 10) {
            uint32_t avg_period = total_time_diff / call_count;
            printf("Average Timer Period: %lu us (Target: %lu us)\n", 
                   avg_period, stats.nextTimerEvent_us);
            
            // Reset counters
            call_count = 0;
            total_time_diff = 0;
        }
    }
    
    last_time = stats.currentTime_us;
}

/* Test function to force different timer periods */
void canopen_timer_test_forcePeriods(void)
{
    static uint32_t test_phase = 0;
    static uint32_t phase_counter = 0;
    
    phase_counter++;
    
    // Change test phase every 50 calls
    if (phase_counter >= 50) {
        test_phase = (test_phase + 1) % 4;
        phase_counter = 0;
        
        switch (test_phase) {
            case 0:
                printf("Test Phase 0: Normal operation\n");
                break;
            case 1:
                printf("Test Phase 1: Force interrupt\n");
                canopen_app_forceInterrupt();
                break;
            case 2:
                printf("Test Phase 2: High frequency test\n");
                break;
            case 3:
                printf("Test Phase 3: Low frequency test\n");
                break;
        }
    }
}

/* Test function to monitor timer events */
void canopen_timer_test_monitorEvents(void)
{
    static uint32_t last_next_timer = 0;
    
    uint32_t current_next_timer = canopen_app_getNextTimerEvent_us();
    
    if (current_next_timer != last_next_timer) {
        printf("Timer Event Changed: %lu -> %lu us\n", 
               last_next_timer, current_next_timer);
        last_next_timer = current_next_timer;
    }
}

/* Main test function - call this periodically from your main loop */
void canopen_timer_test_run(void)
{
    static uint32_t test_counter_main = 0;
    
    test_counter_main++;
    
    // Run different tests based on counter
    switch (test_counter_main % 100) {
        case 0:
            canopen_timer_test_printStats();
            break;
        case 25:
            canopen_timer_test_verifyFrequency();
            break;
        case 50:
            canopen_timer_test_forcePeriods();
            break;
        case 75:
            canopen_timer_test_monitorEvents();
            break;
    }
}

/* Initialize test functions */
void canopen_timer_test_init(void)
{
    printf("CANopen Dynamic Timer Test Initialized\n");
    printf("Call canopen_timer_test_run() periodically to see results\n");
}
