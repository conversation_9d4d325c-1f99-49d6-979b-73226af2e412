/**
 * @file 	CO_interrupt_STM32.h
 * <AUTHOR> Assistant
 * @date 	2024
 * @brief   CANopen interrupt and timer management with FreeRTOS synchronization
 */

#ifndef CO_INTERRUPT_STM32_H
#define CO_INTERRUPT_STM32_H

#ifdef __cplusplus
extern "C" 
{
#endif

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"
#include "CANopen.h"
#include "FreeRTOS.h"
#include "semphr.h"

typedef struct 
{
    uint32_t lastTimeDifference_us;
    uint32_t nextTimerEvent_us;
    uint32_t currentTime_us;
    bool_t timerInitialized;
} CO_TimingStats_t;

typedef struct 
{
    uint32_t min_period_us;
    uint32_t max_period_us;
    uint32_t resolution_us;
    uint32_t timer_clock_hz;
} CO_timer_range_t;

/* Timer functions */
uint32_t getCurrentTime_us(void);
void setTimerPeriod_us(uint32_t period_us);
void CO_timer_init_STM32(void);
void CO_timer_processInterrupt(bool_t *syncWas, uint32_t *timeDifference_us, uint32_t *timerNext_us);
uint32_t CO_timer_getNextTimerEvent_us(void);
void CO_timer_processNextEvent(uint32_t timerNext_us_from_canopen);
void CO_timer_getRange(CO_timer_range_t *range);
void CO_timer_getTimingStats(CO_TimingStats_t *stats);

/* Interrupt synchronization functions */
void CO_interrupt_init(void);
bool CO_interrupt_take(void);
void CO_interrupt_giveFromISR(void);

#ifdef __cplusplus
}
#endif

#endif /* CO_INTERRUPT_STM32_H */
