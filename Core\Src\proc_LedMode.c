/**
 * @file 	proc_LedMode.h
 * <AUTHOR>
 * @date 	30-November-2023
 * @brief   FreeRTOS task header file for managing and displaying different LED patterns
 *          on an Addressable RGB (ARGB) strip based on various operation modes.
 */

#include "proc_LedMode.h"

osThreadId 		LedModeProcHandle;
procLedMode_obj LedModeProcObject;
Timer_obj		*pTimer;
CANOpenData_t   *pCANOpenData;

void LedModeProcInit(void)
{
	osThreadDef(LedModeProc, StartLedModeProc, osPriorityNormal, 0, 256);
	LedModeProcHandle = osThreadCreate(osThread(LedModeProc), NULL);
}

void StartLedModeProc(void const *arguments)
{
	_ARGBInit();

	for(;;)
	{
		_SetMode(&LedModeProcObject, pCANOpenData);
		_SetIsAnimationMode(&LedModeProcObject);
		_SetModeIsChanged(&LedModeProcObject);

		if (LedModeProcObject.ModeIsChanged)
		{
			Timer_ResetCounter(pTimer);
			ARGB_Clear();
			while (ARGB_Show() != ARGB_OK);
		}

		// Set animation time
		ARGB_SetTime(&(LedModeProcObject.argbModule), Timer_GetCounter(pTimer));

		ModeHandler(&LedModeProcObject);
		osDelay(1);
	}
}


void LedModeProc_Init(Timer_obj *Timer, CANOpenData_t *CANOpenData)
{
	pTimer = Timer;
	pCANOpenData = CANOpenData;
	LedModeProcObject.ModeIsChanged = false;

}

bool_t LedModeProc_IsAnimationCompleted(procLedMode_obj *this)
{
	bool_t isAnimationMode = this->isAnimationMode;
	bool_t AnimationIsEnd = this->argbModule.is_running;
	return ((isAnimationMode && AnimationIsEnd) || !isAnimationMode);
}

bool_t LedModeProc_ModeIsChanged(procLedMode_obj *this)
{
	return this->ModeIsChanged;
}

void ModeHandler(procLedMode_obj *this)
{
	switch(this->Mode)
	{
		case Off:		_Mode_Off(this);		return;
		case Init:		_Mode_Init(this);		return;
		case Sleep:		_Mode_Sleep(this);	  	return;
		case Emergency: _Mode_Emergency(this); 	return;
		case Movement: 	_Mode_Movement(this);  	return;
		case Charging: 	_Mode_Charging(this);  	return;
		case Аdjustment:_Mode_Аdjustment(this); return;
		default: 		_Mode_Sleep(this);    	return;
	}
}

void _Mode_Off(procLedMode_obj *this)
{
	ARGB_Clear();
	while (ARGB_Show() != ARGB_OK);
}

void _Mode_Init(procLedMode_obj *this)
{
	ARGB_Fade( &(this->argbModule), 0, NUMB_PIXELS, 255, 255, 255, FADE_MOVESTAND, STRIP_BRIGTNESS);
}

void _Mode_Sleep(procLedMode_obj *this)
{
	ARGB_RainbowColor( &(this->argbModule), 180, 230, UP, 1, 25);
}

void _Mode_Emergency(procLedMode_obj *this)
{
	// Movement error: obstacle detected
	if(this->ModeParam == EMERGENCY_OBSTACLE_DETECTED)
	{
		ARGB_FillRGB(255, 0, 0);
		while (ARGB_Show() == ARGB_BUSY);
	}
	// Movement error: Malfunction
	if(this->ModeParam == EMERGENCY_MALFUNCTION)
	{
		ARGB_Blink( &(this->argbModule), 0, NUMB_PIXELS, 255, 0, 0, BLINK_EMERG_DELAY, BLINK_EMERG_COUNT, BLINK_EMERG_PDELAY);
	}
	if(this->ModeParam == EMERGENCY_NO)
	{
		ARGB_Clear();
		while (ARGB_Show() == ARGB_BUSY);
	}
}

void _Mode_Movement(procLedMode_obj *this)
{
	if(this->ModeParam == MOVEMENT_STATE_STAND)
	{
		ARGB_Fade( &(this->argbModule), 0, NUMB_PIXELS, 0, 255, 0, FADE_MOVESTAND, STRIP_BRIGTNESS);
	}
	else if(this->ModeParam == MOVEMENT_STATE_FORWARD)
	{
		ARGB_FillRGB(0, 255, 0);
		while (ARGB_Show() == ARGB_BUSY);
	}
	else if(this->ModeParam == MOVEMENT_STATE_BACK) //Not used
	{
		if(STRIP_LOCATION == BACK_LEFT || STRIP_LOCATION == BACK_RIGHT)
		{
			ARGB_Blink( &(this->argbModule), 0, NUMB_PIXELS, 255, 102, 0, 250, 5, 0);
		}
		else
		{
			ARGB_Blink( &(this->argbModule), 0, NUMB_PIXELS, 0, 0, 0, 250, 5, 0);
		}
	}
	else if(this->ModeParam == MOVEMENT_STATE_LEFT)
	{
		if(STRIP_LOCATION == FRONT_LEFT || STRIP_LOCATION == BACK_LEFT)
		{
			ARGB_RunningLine( &(this->argbModule), 0, 18, 255, 102, 0, RUNNINGLINE_DELAY,
					RUNNINGLINE_TYPE, RUNNINGLINE_SIZEOFLINE, RUNNINGLINE_PDELAY, DIRECTION_INVERTED);
		}
		else
		{
			ARGB_RunningLine( &(this->argbModule), 0, 18, 0, 0, 0, RUNNINGLINE_DELAY,
					RUNNINGLINE_TYPE, RUNNINGLINE_SIZEOFLINE, RUNNINGLINE_PDELAY, DIRECTION_INVERTED);
		}
	}
	else if(this->ModeParam == MOVEMENT_STATE_RIGHT)
	{
		if(STRIP_LOCATION == FRONT_RIGHT || STRIP_LOCATION == BACK_RIGHT)
		{
			ARGB_RunningLine( &(this->argbModule), 0, 18, 255, 102, 0, RUNNINGLINE_DELAY,
					RUNNINGLINE_TYPE, RUNNINGLINE_SIZEOFLINE, RUNNINGLINE_PDELAY, DIRECTION_INVERTED);
		}
		else
		{
			ARGB_RunningLine( &(this->argbModule), 0, 18, 0, 0, 0, RUNNINGLINE_DELAY,
					RUNNINGLINE_TYPE, RUNNINGLINE_SIZEOFLINE, RUNNINGLINE_PDELAY, DIRECTION_INVERTED);
		}
	}
}

void _Mode_Charging(procLedMode_obj *this)
{
	ARGB_RainbowColor( &(this->argbModule), 120, 150, UP, 1, 15);
}

void _Mode_Аdjustment(procLedMode_obj *this)
{
	ARGB_Fade( &(this->argbModule), 0, NUMB_PIXELS, 255, 0, 0, FADE_MOVESTAND, STRIP_BRIGTNESS);
}

void _ARGBInit(void)
{
	ARGB_Init();
	ARGB_Clear();
	ARGB_SetBrightness(STRIP_BRIGTNESS);
	ARGB_FillRGB(255, 0, 255);
	while (ARGB_Show() == ARGB_BUSY);
}

void _SetMode(procLedMode_obj *this, CANOpenData_t *CANOpenData)
{
	this->Mode = (CANOpenData->LifterStatus == 0x06) ? Аdjustment : CANOpenData->Mode;
	this->ModeParam = CANOpenData->ModeParams;
}

void _SetIsAnimationMode(procLedMode_obj *this)
{
	this->isAnimationMode = ((this->Mode == Sleep) ||
							 (this->Mode == Movement &&
							  this->ModeParam == MOVEMENT_STATE_STAND) ||
							 (this->Mode == Movement &&
							  this->ModeParam == MOVEMENT_STATE_BACK) ||
							 (this->Mode == Movement &&
							  this->ModeParam == MOVEMENT_STATE_LEFT &&
							  (STRIP_LOCATION == FRONT_LEFT)) ||
							 (this->Mode == Movement &&
							  this->ModeParam == MOVEMENT_STATE_LEFT &&
							  (STRIP_LOCATION == FRONT_RIGHT)) ||
							 (this->Mode == Emergency &&
							  this->ModeParam == EMERGENCY_MALFUNCTION) ||
							  this->Mode == Аdjustment);
}

void _SetModeIsChanged(procLedMode_obj *this)
{
	static uint8_t prevMode = 0;
	static uint8_t prevModeParam = 0;

	bool modeChanged = this->Mode != prevMode;
	bool parametersChanged = (this->ModeParam != prevModeParam);

	if(modeChanged || parametersChanged)
	{
		// Update prev val
		prevMode = this->Mode;
		prevModeParam = this->ModeParam;

		this->ModeIsChanged = true;
	}
	else this->ModeIsChanged = false;
}

