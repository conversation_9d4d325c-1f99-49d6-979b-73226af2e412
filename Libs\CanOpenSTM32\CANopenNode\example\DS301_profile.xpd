<?xml version="1.0" encoding="utf-8"?>
<!--File is generated by CANopenEditor v4.0-51-g2d9b1ad, URL: https://github.com/CANopenNode/CANopenEditor-->
<!--File includes additional custom properties for CANopenNode, CANopen protocol stack, URL: https://github.com/CANopenNode/CANopenNode-->
<ISO15745ProfileContainer xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.canopen.org/xml/1.1">
  <ISO15745Profile>
    <ProfileHeader xmlns="">
      <ProfileIdentification>CANopen device profile</ProfileIdentification>
      <ProfileRevision>1.1</ProfileRevision>
      <ProfileName />
      <ProfileSource />
      <ProfileClassID>Device</ProfileClassID>
      <ISO15745Reference>
        <ISO15745Part>1</ISO15745Part>
        <ISO15745Edition>1</ISO15745Edition>
        <ProfileTechnology>CANopen</ProfileTechnology>
      </ISO15745Reference>
    </ProfileHeader>
    <ProfileBody xmlns:q1="http://www.canopen.org/xml/1.1" xsi:type="q1:ProfileBody_Device_CANopen" formatName="CANopen" formatVersion="1.0" fileName="DS301_profile.xpd" fileCreator="" fileCreationDate="2020-11-23" fileCreationTime="13:00:00.0000000+02:00" fileModifiedBy="" fileModificationDate="2021-08-09" fileModificationTime="18:39:55.9605360+02:00" fileVersion="1" supportedLanguages="en" xmlns="">
      <q1:DeviceIdentity>
        <q1:vendorName></q1:vendorName>
        <q1:vendorID></q1:vendorID>
        <q1:productName>New Product</q1:productName>
        <q1:productID></q1:productID>
        <q1:version versionType="SW">0</q1:version>
        <q1:version versionType="FW">0</q1:version>
        <q1:version versionType="HW">0</q1:version>
      </q1:DeviceIdentity>
      <q1:DeviceFunction>
        <q1:capabilities>
          <q1:characteristicsList>
            <q1:characteristic>
              <q1:characteristicName>
                <label lang="en">SW library</label>
              </q1:characteristicName>
              <q1:characteristicContent>
                <label lang="en">libedssharp</label>
              </q1:characteristicContent>
              <q1:characteristicContent>
                <label lang="en">CANopenNode</label>
              </q1:characteristicContent>
            </q1:characteristic>
          </q1:characteristicsList>
        </q1:capabilities>
      </q1:DeviceFunction>
      <q1:ApplicationProcess>
        <q1:dataTypeList>
          <q1:array name="Pre-defined error field" uniqueID="UID_ARR_1003">
            <q1:subrange lowerLimit="0" upperLimit="16" />
            <UDINT />
          </q1:array>
          <q1:array name="Store parameters" uniqueID="UID_ARR_1010">
            <q1:subrange lowerLimit="0" upperLimit="4" />
            <UDINT />
          </q1:array>
          <q1:array name="Restore default parameters" uniqueID="UID_ARR_1011">
            <q1:subrange lowerLimit="0" upperLimit="4" />
            <UDINT />
          </q1:array>
          <q1:array name="Consumer heartbeat time" uniqueID="UID_ARR_1016">
            <q1:subrange lowerLimit="0" upperLimit="8" />
            <UDINT />
          </q1:array>
          <q1:array name="Verify configuration" uniqueID="UID_ARR_1020">
            <q1:subrange lowerLimit="0" upperLimit="2" />
            <UDINT />
          </q1:array>
          <q1:array name="OS prompt" uniqueID="UID_ARR_1026">
            <q1:subrange lowerLimit="0" upperLimit="3" />
            <USINT />
          </q1:array>
          <q1:array name="Module list" uniqueID="UID_ARR_1027">
            <q1:subrange lowerLimit="0" upperLimit="1" />
            <UINT />
          </q1:array>
          <q1:array name="Emergency consumer object" uniqueID="UID_ARR_1028">
            <q1:subrange lowerLimit="0" upperLimit="8" />
            <UDINT />
          </q1:array>
          <q1:array name="Error behavior object" uniqueID="UID_ARR_1029">
            <q1:subrange lowerLimit="0" upperLimit="2" />
            <USINT />
          </q1:array>
          <q1:struct name="Identity" uniqueID="UID_REC_1018">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_101800">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Vendor-ID" uniqueID="UID_RECSUB_101801">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Product code" uniqueID="UID_RECSUB_101802">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Revision number" uniqueID="UID_RECSUB_101803">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Serial number" uniqueID="UID_RECSUB_101804">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="OS command" uniqueID="UID_REC_1023">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_102300">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Command" uniqueID="UID_RECSUB_102301">
              <BITSTRING />
            </q1:varDeclaration>
            <q1:varDeclaration name="Status" uniqueID="UID_RECSUB_102302">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Reply" uniqueID="UID_RECSUB_102303">
              <BITSTRING />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="OS debugger interface" uniqueID="UID_REC_1025">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_102500">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Command" uniqueID="UID_RECSUB_102501">
              <BITSTRING />
            </q1:varDeclaration>
            <q1:varDeclaration name="Status" uniqueID="UID_RECSUB_102502">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Reply" uniqueID="UID_RECSUB_102503">
              <BITSTRING />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="SDO server parameter" uniqueID="UID_REC_1200">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_120000">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID client to server (rx)" uniqueID="UID_RECSUB_120001">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID server to client (tx)" uniqueID="UID_RECSUB_120002">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="SDO server parameter" uniqueID="UID_REC_1201">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_120100">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID client to server (rx)" uniqueID="UID_RECSUB_120101">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID server to client (tx)" uniqueID="UID_RECSUB_120102">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Node-ID of the SDO client" uniqueID="UID_RECSUB_120103">
              <USINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="SDO client parameter" uniqueID="UID_REC_1280">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_128000">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID client to server (tx)" uniqueID="UID_RECSUB_128001">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID server to client (rx)" uniqueID="UID_RECSUB_128002">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Node-ID of the SDO server" uniqueID="UID_RECSUB_128003">
              <USINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO communication parameter" uniqueID="UID_REC_1400">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_140000">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by RPDO" uniqueID="UID_RECSUB_140001">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_140002">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_140005">
              <UINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO communication parameter" uniqueID="UID_REC_1401">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_140100">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by RPDO" uniqueID="UID_RECSUB_140101">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_140102">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_140105">
              <UINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO communication parameter" uniqueID="UID_REC_1402">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_140200">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by RPDO" uniqueID="UID_RECSUB_140201">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_140202">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_140205">
              <UINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO communication parameter" uniqueID="UID_REC_1403">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_140300">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by RPDO" uniqueID="UID_RECSUB_140301">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_140302">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_140305">
              <UINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO communication parameter" uniqueID="UID_REC_1404">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_140400">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by RPDO" uniqueID="UID_RECSUB_140401">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_140402">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_140405">
              <UINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO mapping parameter" uniqueID="UID_REC_1600">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_160000">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_160001">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_160002">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_160003">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_160004">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_160005">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_160006">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_160007">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_160008">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO mapping parameter" uniqueID="UID_REC_1601">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_160100">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_160101">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_160102">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_160103">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_160104">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_160105">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_160106">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_160107">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_160108">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO mapping parameter" uniqueID="UID_REC_1602">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_160200">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_160201">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_160202">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_160203">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_160204">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_160205">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_160206">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_160207">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_160208">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO mapping parameter" uniqueID="UID_REC_1603">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_160300">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_160301">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_160302">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_160303">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_160304">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_160305">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_160306">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_160307">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_160308">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="RPDO mapping parameter" uniqueID="UID_REC_1604">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_160400">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_160401">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_160402">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_160403">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_160404">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_160405">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_160406">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_160407">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_160408">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO communication parameter" uniqueID="UID_REC_1800">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_180000">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by TPDO" uniqueID="UID_RECSUB_180001">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_180002">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Inhibit time" uniqueID="UID_RECSUB_180003">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_180005">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="SYNC start value" uniqueID="UID_RECSUB_180006">
              <USINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO communication parameter" uniqueID="UID_REC_1801">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_180100">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by TPDO" uniqueID="UID_RECSUB_180101">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_180102">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Inhibit time" uniqueID="UID_RECSUB_180103">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_180105">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="SYNC start value" uniqueID="UID_RECSUB_180106">
              <USINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO communication parameter" uniqueID="UID_REC_1802">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_180200">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by TPDO" uniqueID="UID_RECSUB_180201">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_180202">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Inhibit time" uniqueID="UID_RECSUB_180203">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_180205">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="SYNC start value" uniqueID="UID_RECSUB_180206">
              <USINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO communication parameter" uniqueID="UID_REC_1803">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_180300">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by TPDO" uniqueID="UID_RECSUB_180301">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_180302">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Inhibit time" uniqueID="UID_RECSUB_180303">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_180305">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="SYNC start value" uniqueID="UID_RECSUB_180306">
              <USINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO communication parameter" uniqueID="UID_REC_1804">
            <q1:varDeclaration name="Highest sub-index supported" uniqueID="UID_RECSUB_180400">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="COB-ID used by TPDO" uniqueID="UID_RECSUB_180401">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Transmission type" uniqueID="UID_RECSUB_180402">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Inhibit time" uniqueID="UID_RECSUB_180403">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Event timer" uniqueID="UID_RECSUB_180405">
              <UINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="SYNC start value" uniqueID="UID_RECSUB_180406">
              <USINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO mapping parameter" uniqueID="UID_REC_1A00">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_1A0000">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_1A0001">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_1A0002">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_1A0003">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_1A0004">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_1A0005">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_1A0006">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_1A0007">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_1A0008">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO mapping parameter" uniqueID="UID_REC_1A01">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_1A0100">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_1A0101">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_1A0102">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_1A0103">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_1A0104">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_1A0105">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_1A0106">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_1A0107">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_1A0108">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO mapping parameter" uniqueID="UID_REC_1A02">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_1A0200">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_1A0201">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_1A0202">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_1A0203">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_1A0204">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_1A0205">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_1A0206">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_1A0207">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_1A0208">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO mapping parameter" uniqueID="UID_REC_1A03">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_1A0300">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_1A0301">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_1A0302">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_1A0303">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_1A0304">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_1A0305">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_1A0306">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_1A0307">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_1A0308">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
          <q1:struct name="TPDO mapping parameter" uniqueID="UID_REC_1A04">
            <q1:varDeclaration name="Number of mapped application objects in PDO" uniqueID="UID_RECSUB_1A0400">
              <USINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 1" uniqueID="UID_RECSUB_1A0401">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 2" uniqueID="UID_RECSUB_1A0402">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 3" uniqueID="UID_RECSUB_1A0403">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 4" uniqueID="UID_RECSUB_1A0404">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 5" uniqueID="UID_RECSUB_1A0405">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 6" uniqueID="UID_RECSUB_1A0406">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 7" uniqueID="UID_RECSUB_1A0407">
              <UDINT />
            </q1:varDeclaration>
            <q1:varDeclaration name="Application object 8" uniqueID="UID_RECSUB_1A0408">
              <UDINT />
            </q1:varDeclaration>
          </q1:struct>
        </q1:dataTypeList>
        <q1:parameterList>
          <q1:parameter uniqueID="UID_OBJ_1000">
            <description lang="en">* bit 16-31: Additional information
* bit 0-15: Device profile number</description>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
            <q1:property name="CO_countLabel" value="NMT" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1001">
            <description lang="en">* bit 7: manufacturer specific
* bit 6: Reserved (always 0)
* bit 5: device profile specific
* bit 4: communication error (overrun, error state)
* bit 3: temperature
* bit 2: voltage
* bit 1: current
* bit 0: generic error</description>
            <USINT />
            <q1:defaultValue value="0x00" />
            <q1:property name="CO_countLabel" value="EM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1002">
            <label lang="en">Manufacturer status register</label>
            <UDINT />
            <q1:defaultValue value="0" />
            <q1:property name="CO_disabled" value="true" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1003">
            <description lang="en">* Sub Index 0: Contains number of actual errors. 0 can be written to clear error history.
* sub-index 1 and above:
  * bit 16-31: Manufacturer specific additional information
  * bit 0-15: Error code as transmited in the Emergency object</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1003" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100300" access="readWrite">
            <label lang="en">Number of errors</label>
            <USINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100301">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100302">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100303">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100304">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100305">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100306">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100307">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100308">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100309">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_10030A">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_10030B">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_10030C">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_10030D">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_10030E">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_10030F">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_100310">
            <label lang="en">Standard error field</label>
            <UDINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1005" access="readWrite">
            <description lang="en">* bit 31: set to 0
* bit 30: If set, CANopen device generates SYNC object
* bit 11-29: set to 0
* bit 0-10: 11-bit CAN-ID</description>
            <UDINT />
            <q1:defaultValue value="0x00000080" />
            <q1:property name="CO_countLabel" value="SYNC" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1006" access="readWrite">
            <description lang="en">Period of SYNC transmission in µs (0 = transmission disabled).</description>
            <UDINT />
            <q1:defaultValue value="0" />
            <q1:property name="CO_countLabel" value="SYNC_PROD" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1007" access="readWrite">
            <description lang="en">Synchronous window leghth in µs (0 = not used). All synchronous PDOs must be transmitted within this time window.</description>
            <UDINT />
            <q1:defaultValue value="0" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1008">
            <label lang="en">Manufacturer device name</label>
            <STRING />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1009">
            <label lang="en">Manufacturer hardware version</label>
            <STRING />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_100A">
            <label lang="en">Manufacturer software version</label>
            <STRING />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1010">
            <description lang="en">Sub-indexes 1 and above:
* Reading provides information about its storage functionality:
  * bit 1: If set, CANopen device saves parameters autonomously
  * bit 0: If set, CANopen device saves parameters on command
* Writing value 0x65766173 ('s','a','v','e' from LSB to MSB) stores corresponding data.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1010" />
            <q1:property name="CO_countLabel" value="STORAGE" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101000">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x04" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101001" access="readWrite">
            <label lang="en">Save all parameters</label>
            <UDINT />
            <q1:defaultValue value="0x00000001" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101002" access="readWrite">
            <label lang="en">Save communication parameters</label>
            <UDINT />
            <q1:defaultValue value="0x00000001" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101003" access="readWrite">
            <label lang="en">Save application parameters</label>
            <UDINT />
            <q1:defaultValue value="0x00000001" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101004" access="readWrite">
            <label lang="en">Save manufacturer defined parameters</label>
            <UDINT />
            <q1:defaultValue value="0x00000001" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1011">
            <description lang="en">Sub-indexes 1 and above:
* Reading provides information about its restoring capability:
  * bit 0: If set, CANopen device restores parameters
* Writing value 0x64616F6C ('l','o','a','d' from LSB to MSB) restores corresponding data.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1011" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101100">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x04" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101101" access="readWrite">
            <label lang="en">Restore all default parameters</label>
            <UDINT />
            <q1:defaultValue value="0x00000001" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101102" access="readWrite">
            <label lang="en">Restore communication default parameters</label>
            <UDINT />
            <q1:defaultValue value="0x00000001" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101103" access="readWrite">
            <label lang="en">Restore application default parameters</label>
            <UDINT />
            <q1:defaultValue value="0x00000001" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101104" access="readWrite">
            <label lang="en">Restore manufacturer defined default parameters</label>
            <UDINT />
            <q1:defaultValue value="0x00000001" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1012" access="readWrite">
            <description lang="en">* bit 31: If set, CANopen device consumes TIME message
* bit 30: If set, CANopen device produces TIME message
* bit 11-29: set to 0
* bit 0-10: 11-bit CAN-ID</description>
            <UDINT />
            <q1:defaultValue value="0x00000100" />
            <q1:property name="CO_countLabel" value="TIME" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1013" access="readWrite">
            <label lang="en">High resolution time stamp</label>
            <UDINT />
            <q1:defaultValue value="0" />
            <q1:property name="CO_disabled" value="true" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1014" access="readWrite">
            <description lang="en">* bit 31: If set, EMCY does NOT exist / is NOT valid
* bit 11-30: set to 0
* bit 0-10: 11-bit CAN-ID</description>
            <UDINT />
            <q1:defaultValue value="0x80+$NODEID" />
            <q1:property name="CO_countLabel" value="EM_PROD" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1015" access="readWrite">
            <description lang="en">Inhibit time of emergency message in multiples of 100µs. The value 0 disables the inhibit time.</description>
            <UINT />
            <q1:defaultValue value="0" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1016">
            <description lang="en">Consumer Heartbeat Time:
  * bit 24-31: set to 0
  * bit 16-23: Node ID of the monitored node. If 0 or greater than 127, sub-entry is not used.
  * bit 0-15: Heartbeat time in ms (if 0, sub-intry is not used). Value should be higher than the corresponding producer heartbeat time.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1016" />
            <q1:property name="CO_countLabel" value="HB_CONS" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101600">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x08" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101601" access="readWrite">
            <label lang="en">Consumer heartbeat time</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101602" access="readWrite">
            <label lang="en">Consumer heartbeat time</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101603" access="readWrite">
            <label lang="en">Consumer heartbeat time</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101604" access="readWrite">
            <label lang="en">Consumer heartbeat time</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101605" access="readWrite">
            <label lang="en">Consumer heartbeat time</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101606" access="readWrite">
            <label lang="en">Consumer heartbeat time</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101607" access="readWrite">
            <label lang="en">Consumer heartbeat time</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101608" access="readWrite">
            <label lang="en">Consumer heartbeat time</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1017" access="readWrite">
            <description lang="en">Heartbeat producer time in ms (0 = disable transmission).</description>
            <UINT />
            <q1:defaultValue value="0" />
            <q1:property name="CO_countLabel" value="HB_PROD" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1018">
            <description lang="en">* Vendor-ID, assigned by CiA
* Product code, manufacturer specific
* Revision number:
  * bit 16-31: Major revision number (CANopen behavior has changed)
  * bit 0-15: Minor revision num. (CANopen behavior has not changed)
* Serial number, manufacturer specific</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1018" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101800">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x04" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101801">
            <label lang="en">Vendor-ID</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101802">
            <label lang="en">Product code</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101803">
            <label lang="en">Revision number</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_101804">
            <label lang="en">Serial number</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1019" access="readWrite">
            <description lang="en">* Value 0: SYNC message is transmitted with data length 0.
* Value 1: reserved.
* Value 2-240: SYNC message has one data byte, which contains the counter.
* Value 241-255: reserved.</description>
            <USINT />
            <q1:defaultValue value="0" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1020">
            <label lang="en">Verify configuration</label>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1020" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102000">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x02" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102001" access="readWrite">
            <label lang="en">Configuration date</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102002" access="readWrite">
            <label lang="en">Configuration time</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1021">
            <label lang="en">Store EDS</label>
            <BITSTRING />
            <q1:property name="CO_disabled" value="true" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1022">
            <label lang="en">Store format</label>
            <UINT />
            <q1:defaultValue value="0x0000" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1023">
            <label lang="en">OS command</label>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1023" />
            <q1:property name="CO_disabled" value="true" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102300">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x03" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102301" access="readWrite">
            <label lang="en">Command</label>
            <BITSTRING />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102302">
            <label lang="en">Status</label>
            <USINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102303">
            <label lang="en">Reply</label>
            <BITSTRING />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1024" access="write">
            <label lang="en">OS command mode</label>
            <USINT />
            <q1:defaultValue value="0x00" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1025">
            <label lang="en">OS debugger interface</label>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1025" />
            <q1:property name="CO_disabled" value="true" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102500">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x03" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102501" access="readWrite">
            <label lang="en">Command</label>
            <BITSTRING />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102502">
            <label lang="en">Status</label>
            <USINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102503">
            <label lang="en">Reply</label>
            <BITSTRING />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1026">
            <label lang="en">OS prompt</label>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1026" />
            <q1:property name="CO_disabled" value="true" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102600">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x03" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102601" access="readWrite">
            <label lang="en">StdIn</label>
            <USINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102602" access="readWrite">
            <label lang="en">StdOut</label>
            <USINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102603" access="readWrite">
            <label lang="en">StdErr</label>
            <USINT />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1027">
            <label lang="en">Module list</label>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1027" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102700">
            <label lang="en">Number of connected modules</label>
            <USINT />
            <q1:defaultValue value="1" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102701">
            <label lang="en">Module 1</label>
            <UINT />
            <q1:defaultValue value="0x0000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1028">
            <description lang="en">Emergency consumer 1 to 127:
* bit 31: If set, EMCY consumer does NOT exist / is NOT valid
* bit 11-30: set to 0
* bit 0-10: 11-bit CAN-ID</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1028" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102800">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x08" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102801" access="readWrite">
            <label lang="en">Emergency consumer 1</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102802" access="readWrite">
            <label lang="en">Emergency consumer 2</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102803" access="readWrite">
            <label lang="en">Emergency consumer 3</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102804" access="readWrite">
            <label lang="en">Emergency consumer 4</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102805" access="readWrite">
            <label lang="en">Emergency consumer 5</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102806" access="readWrite">
            <label lang="en">Emergency consumer 6</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102807" access="readWrite">
            <label lang="en">Emergency consumer 7</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102808" access="readWrite">
            <label lang="en">Emergency consumer 8</label>
            <UDINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1029">
            <description lang="en">Sub-indexes 1 and above:
* Value 0x00: on error change to NMT state Pre-operational (only if currently in NMT state Operational)
* Value 0x01: on error do nothing
* Value 0x02: on error change to NMT state Stopped</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_ARR_1029" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102900">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x02" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102901" access="readWrite">
            <label lang="en">Communication error</label>
            <USINT />
            <q1:defaultValue value="0x00" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_102902" access="readWrite">
            <label lang="en">Profile- or manufacturer-specific error</label>
            <USINT />
            <q1:defaultValue value="0x00" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1200">
            <description lang="en">Sub-indexes 1 and 2:
* bit 11-31: set to 0
* bit 0-10: 11-bit CAN-ID</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1200" />
            <q1:property name="CO_countLabel" value="SDO_SRV" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_120000">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="2" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_120001">
            <label lang="en">COB-ID client to server (rx)</label>
            <UDINT />
            <q1:defaultValue value="0x600+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_120002">
            <label lang="en">COB-ID server to client (tx)</label>
            <UDINT />
            <q1:defaultValue value="0x580+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1201">
            <description lang="en">* Sub-indexes 1 and 2:
  * bit 31: If set, SDO does NOT exist / is NOT valid
  * bit 30: If set, value is assigned dynamically
  * bit 11-29: set to 0
  * bit 0-10: 11-bit CAN-ID
* Node-ID of the SDO client, 0x01 to 0x7F</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1201" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_countLabel" value="SDO_SRV" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_120100">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x03" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_120101" access="readWrite">
            <label lang="en">COB-ID client to server (rx)</label>
            <UDINT />
            <q1:defaultValue value="0x80000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_120102" access="readWrite">
            <label lang="en">COB-ID server to client (tx)</label>
            <UDINT />
            <q1:defaultValue value="0x80000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_120103" access="readWrite">
            <label lang="en">Node-ID of the SDO client</label>
            <USINT />
            <q1:defaultValue value="0x01" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1280">
            <description lang="en">* Sub-indexes 1 and 2:
  * bit 31: If set, SDO does NOT exist / is NOT valid
  * bit 30: If set, value is assigned dynamically
  * bit 11-29: set to 0
  * bit 0-10: 11-bit CAN-ID
* Node-ID of the SDO server, 0x01 to 0x7F</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1280" />
            <q1:property name="CO_countLabel" value="SDO_CLI" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_128000">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x03" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_128001" access="readWrite">
            <label lang="en">COB-ID client to server (tx)</label>
            <UDINT />
            <q1:defaultValue value="0x80000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_128002" access="readWrite">
            <label lang="en">COB-ID server to client (rx)</label>
            <UDINT />
            <q1:defaultValue value="0x80000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_128003" access="readWrite">
            <label lang="en">Node-ID of the SDO server</label>
            <USINT />
            <q1:defaultValue value="0x01" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1400">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 11-30: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0-240: synchronous, processed after next reception of SYNC object
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Event timer in ms (0 = disabled) for deadline monitoring.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1400" />
            <q1:property name="CO_countLabel" value="RPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140000">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x05" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140001" access="readWrite">
            <label lang="en">COB-ID used by RPDO</label>
            <UDINT />
            <q1:defaultValue value="0x80000200+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140002" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140005" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1401">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 11-30: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0-240: synchronous, processed after next reception of SYNC object
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Event timer in ms (0 = disabled) for deadline monitoring.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1401" />
            <q1:property name="CO_countLabel" value="RPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140100">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x05" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140101" access="readWrite">
            <label lang="en">COB-ID used by RPDO</label>
            <UDINT />
            <q1:defaultValue value="0x80000300+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140102" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140105" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1402">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 11-30: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0-240: synchronous, processed after next reception of SYNC object
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Event timer in ms (0 = disabled) for deadline monitoring.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1402" />
            <q1:property name="CO_countLabel" value="RPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140200">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x05" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140201" access="readWrite">
            <label lang="en">COB-ID used by RPDO</label>
            <UDINT />
            <q1:defaultValue value="0x80000400+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140202" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140205" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1403">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 11-30: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0-240: synchronous, processed after next reception of SYNC object
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Event timer in ms (0 = disabled) for deadline monitoring.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1403" />
            <q1:property name="CO_countLabel" value="RPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140300">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x05" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140301" access="readWrite">
            <label lang="en">COB-ID used by RPDO</label>
            <UDINT />
            <q1:defaultValue value="0x80000500+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140302" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140305" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1404">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 11-30: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0-240: synchronous, processed after next reception of SYNC object
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Event timer in ms (0 = disabled) for deadline monitoring.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1404" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_countLabel" value="RPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140400">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x05" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140401" access="readWrite">
            <label lang="en">COB-ID used by RPDO</label>
            <UDINT />
            <q1:defaultValue value="0x80000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140402" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_140405" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1600">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1600" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160000" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160001" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160002" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160003" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160004" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160005" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160006" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160007" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160008" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1601">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1601" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160100" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160101" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160102" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160103" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160104" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160105" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160106" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160107" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160108" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1602">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1602" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160200" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160201" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160202" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160203" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160204" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160205" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160206" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160207" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160208" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1603">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1603" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160300" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160301" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160302" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160303" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160304" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160305" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160306" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160307" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160308" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1604">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1604" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160400" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160401" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160402" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160403" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160404" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160405" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160406" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160407" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_160408" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1800">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 30: If set, NO RTR is allowed on this PDO
  * bit 11-29: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0: synchronous (acyclic)
  * Value 1-240: synchronous (cyclic every (1-240)-th sync)
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Inhibit time in multiple of 100µs, if the transmission type is set to 254 or 255 (0 = disabled).
* Event timer interval in ms, if the transmission type is set to 254 or 255 (0 = disabled).
* SYNC start value
  * Value 0: Counter of the SYNC message shall not be processed.
  * Value 1-240: The SYNC message with the counter value equal to this value shall be regarded as the first received SYNC message.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1800" />
            <q1:property name="CO_countLabel" value="TPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180000">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x06" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180001" access="readWrite">
            <label lang="en">COB-ID used by TPDO</label>
            <UDINT />
            <q1:defaultValue value="0xC0000180+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180002" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180003" access="readWrite">
            <label lang="en">Inhibit time</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180005" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180006" access="readWrite">
            <label lang="en">SYNC start value</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1801">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 30: If set, NO RTR is allowed on this PDO
  * bit 11-29: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0: synchronous (acyclic)
  * Value 1-240: synchronous (cyclic every (1-240)-th sync)
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Inhibit time in multiple of 100µs, if the transmission type is set to 254 or 255 (0 = disabled).
* Event timer interval in ms, if the transmission type is set to 254 or 255 (0 = disabled).
* SYNC start value
  * Value 0: Counter of the SYNC message shall not be processed.
  * Value 1-240: The SYNC message with the counter value equal to this value shall be regarded as the first received SYNC message.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1801" />
            <q1:property name="CO_countLabel" value="TPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180100">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x06" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180101" access="readWrite">
            <label lang="en">COB-ID used by TPDO</label>
            <UDINT />
            <q1:defaultValue value="0xC0000280+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180102" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180103" access="readWrite">
            <label lang="en">Inhibit time</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180105" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180106" access="readWrite">
            <label lang="en">SYNC start value</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1802">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 30: If set, NO RTR is allowed on this PDO
  * bit 11-29: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0: synchronous (acyclic)
  * Value 1-240: synchronous (cyclic every (1-240)-th sync)
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Inhibit time in multiple of 100µs, if the transmission type is set to 254 or 255 (0 = disabled).
* Event timer interval in ms, if the transmission type is set to 254 or 255 (0 = disabled).
* SYNC start value
  * Value 0: Counter of the SYNC message shall not be processed.
  * Value 1-240: The SYNC message with the counter value equal to this value shall be regarded as the first received SYNC message.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1802" />
            <q1:property name="CO_countLabel" value="TPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180200">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x06" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180201" access="readWrite">
            <label lang="en">COB-ID used by TPDO</label>
            <UDINT />
            <q1:defaultValue value="0xC0000380+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180202" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180203" access="readWrite">
            <label lang="en">Inhibit time</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180205" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180206" access="readWrite">
            <label lang="en">SYNC start value</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1803">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 30: If set, NO RTR is allowed on this PDO
  * bit 11-29: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0: synchronous (acyclic)
  * Value 1-240: synchronous (cyclic every (1-240)-th sync)
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Inhibit time in multiple of 100µs, if the transmission type is set to 254 or 255 (0 = disabled).
* Event timer interval in ms, if the transmission type is set to 254 or 255 (0 = disabled).
* SYNC start value
  * Value 0: Counter of the SYNC message shall not be processed.
  * Value 1-240: The SYNC message with the counter value equal to this value shall be regarded as the first received SYNC message.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1803" />
            <q1:property name="CO_countLabel" value="TPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180300">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x06" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180301" access="readWrite">
            <label lang="en">COB-ID used by TPDO</label>
            <UDINT />
            <q1:defaultValue value="0xC0000480+$NODEID" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180302" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180303" access="readWrite">
            <label lang="en">Inhibit time</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180305" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180306" access="readWrite">
            <label lang="en">SYNC start value</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1804">
            <description lang="en">* COB-ID used by RPDO:
  * bit 31: If set, PDO does not exist / is not valid
  * bit 30: If set, NO RTR is allowed on this PDO
  * bit 11-29: set to 0
  * bit 0-10: 11-bit CAN-ID
* Transmission type:
  * Value 0: synchronous (acyclic)
  * Value 1-240: synchronous (cyclic every (1-240)-th sync)
  * Value 241-253: not used
  * Value 254: event-driven (manufacturer-specific)
  * Value 255: event-driven (device profile and application profile specific)
* Inhibit time in multiple of 100µs, if the transmission type is set to 254 or 255 (0 = disabled).
* Event timer interval in ms, if the transmission type is set to 254 or 255 (0 = disabled).
* SYNC start value
  * Value 0: Counter of the SYNC message shall not be processed.
  * Value 1-240: The SYNC message with the counter value equal to this value shall be regarded as the first received SYNC message.</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1804" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_countLabel" value="TPDO" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180400">
            <label lang="en">Highest sub-index supported</label>
            <USINT />
            <q1:defaultValue value="0x06" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180401" access="readWrite">
            <label lang="en">COB-ID used by TPDO</label>
            <UDINT />
            <q1:defaultValue value="0xC0000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180402" access="readWrite">
            <label lang="en">Transmission type</label>
            <USINT />
            <q1:defaultValue value="254" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180403" access="readWrite">
            <label lang="en">Inhibit time</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180405" access="readWrite">
            <label lang="en">Event timer</label>
            <UINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_180406" access="readWrite">
            <label lang="en">SYNC start value</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1A00">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1A00" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0000" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0001" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0002" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0003" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0004" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0005" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0006" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0007" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0008" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1A01">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1A01" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0100" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0101" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0102" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0103" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0104" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0105" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0106" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0107" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0108" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1A02">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1A02" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0200" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0201" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0202" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0203" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0204" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0205" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0206" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0207" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0208" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1A03">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1A03" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0300" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0301" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0302" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0303" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0304" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0305" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0306" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0307" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0308" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_OBJ_1A04">
            <description lang="en">* Number of mapped application objects in PDO:
  * Value 0: mapping is disabled.
  * Value 1: sub-index 0x01 is valid.
  * Value 2-8: sub-indexes 0x01 to (0x02 to 0x08) are valid.
* Application object 1-8:
  * bit 16-31: index
  * bit 8-15: sub-index
  * bit 0-7: data length in bits</description>
            <q1:dataTypeIDRef uniqueIDRef="UID_REC_1A04" />
            <q1:property name="CO_disabled" value="true" />
            <q1:property name="CO_storageGroup" value="PERSIST_COMM" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0400" access="readWrite">
            <label lang="en">Number of mapped application objects in PDO</label>
            <USINT />
            <q1:defaultValue value="0" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0401" access="readWrite">
            <label lang="en">Application object 1</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0402" access="readWrite">
            <label lang="en">Application object 2</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0403" access="readWrite">
            <label lang="en">Application object 3</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0404" access="readWrite">
            <label lang="en">Application object 4</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0405" access="readWrite">
            <label lang="en">Application object 5</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0406" access="readWrite">
            <label lang="en">Application object 6</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0407" access="readWrite">
            <label lang="en">Application object 7</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
          <q1:parameter uniqueID="UID_SUB_1A0408" access="readWrite">
            <label lang="en">Application object 8</label>
            <UDINT />
            <q1:defaultValue value="0x00000000" />
          </q1:parameter>
        </q1:parameterList>
      </q1:ApplicationProcess>
    </ProfileBody>
  </ISO15745Profile>
  <ISO15745Profile>
    <ProfileHeader xmlns="">
      <ProfileIdentification>CANopen communication network profile</ProfileIdentification>
      <ProfileRevision>1.1</ProfileRevision>
      <ProfileName />
      <ProfileSource />
      <ProfileClassID>CommunicationNetwork</ProfileClassID>
      <ISO15745Reference>
        <ISO15745Part>1</ISO15745Part>
        <ISO15745Edition>1</ISO15745Edition>
        <ProfileTechnology>CANopen</ProfileTechnology>
      </ISO15745Reference>
    </ProfileHeader>
    <ProfileBody xmlns:q2="http://www.canopen.org/xml/1.1" xsi:type="q2:ProfileBody_CommunicationNetwork_CANopen" formatName="CANopen" formatVersion="1.0" fileName="DS301_profile.xpd" fileCreator="" fileCreationDate="2020-11-23" fileCreationTime="13:00:00.0000000+02:00" fileModificationDate="2021-08-09" fileModificationTime="18:39:55.9605360+02:00" fileVersion="1" supportedLanguages="en" xmlns="">
      <ApplicationLayers>
        <q2:CANopenObjectList>
          <CANopenObject index="1000" name="Device type" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1000" />
          <CANopenObject index="1001" name="Error register" objectType="7" PDOmapping="TPDO" uniqueIDRef="UID_OBJ_1001" />
          <CANopenObject index="1002" name="Manufacturer status register" objectType="7" PDOmapping="optional" uniqueIDRef="UID_OBJ_1002" />
          <CANopenObject index="1003" name="Pre-defined error field" objectType="8" uniqueIDRef="UID_OBJ_1003" subNumber="17">
            <CANopenSubObject subIndex="00" name="Number of errors" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100300" />
            <CANopenSubObject subIndex="01" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100301" />
            <CANopenSubObject subIndex="02" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100302" />
            <CANopenSubObject subIndex="03" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100303" />
            <CANopenSubObject subIndex="04" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100304" />
            <CANopenSubObject subIndex="05" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100305" />
            <CANopenSubObject subIndex="06" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100306" />
            <CANopenSubObject subIndex="07" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100307" />
            <CANopenSubObject subIndex="08" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100308" />
            <CANopenSubObject subIndex="09" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100309" />
            <CANopenSubObject subIndex="0A" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_10030A" />
            <CANopenSubObject subIndex="0B" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_10030B" />
            <CANopenSubObject subIndex="0C" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_10030C" />
            <CANopenSubObject subIndex="0D" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_10030D" />
            <CANopenSubObject subIndex="0E" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_10030E" />
            <CANopenSubObject subIndex="0F" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_10030F" />
            <CANopenSubObject subIndex="10" name="Standard error field" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_100310" />
          </CANopenObject>
          <CANopenObject index="1005" name="COB-ID SYNC message" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1005" />
          <CANopenObject index="1006" name="Communication cycle period" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1006" />
          <CANopenObject index="1007" name="Synchronous window length" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1007" />
          <CANopenObject index="1008" name="Manufacturer device name" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1008" />
          <CANopenObject index="1009" name="Manufacturer hardware version" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1009" />
          <CANopenObject index="100A" name="Manufacturer software version" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_100A" />
          <CANopenObject index="1010" name="Store parameters" objectType="8" uniqueIDRef="UID_OBJ_1010" subNumber="5">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101000" />
            <CANopenSubObject subIndex="01" name="Save all parameters" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101001" />
            <CANopenSubObject subIndex="02" name="Save communication parameters" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101002" />
            <CANopenSubObject subIndex="03" name="Save application parameters" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101003" />
            <CANopenSubObject subIndex="04" name="Save manufacturer defined parameters" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101004" />
          </CANopenObject>
          <CANopenObject index="1011" name="Restore default parameters" objectType="8" uniqueIDRef="UID_OBJ_1011" subNumber="5">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101100" />
            <CANopenSubObject subIndex="01" name="Restore all default parameters" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101101" />
            <CANopenSubObject subIndex="02" name="Restore communication default parameters" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101102" />
            <CANopenSubObject subIndex="03" name="Restore application default parameters" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101103" />
            <CANopenSubObject subIndex="04" name="Restore manufacturer defined default parameters" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101104" />
          </CANopenObject>
          <CANopenObject index="1012" name="COB-ID time stamp object" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1012" />
          <CANopenObject index="1013" name="High resolution time stamp" objectType="7" PDOmapping="optional" uniqueIDRef="UID_OBJ_1013" />
          <CANopenObject index="1014" name="COB-ID EMCY" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1014" />
          <CANopenObject index="1015" name="Inhibit time EMCY" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1015" />
          <CANopenObject index="1016" name="Consumer heartbeat time" objectType="8" uniqueIDRef="UID_OBJ_1016" subNumber="9">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101600" />
            <CANopenSubObject subIndex="01" name="Consumer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101601" />
            <CANopenSubObject subIndex="02" name="Consumer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101602" />
            <CANopenSubObject subIndex="03" name="Consumer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101603" />
            <CANopenSubObject subIndex="04" name="Consumer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101604" />
            <CANopenSubObject subIndex="05" name="Consumer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101605" />
            <CANopenSubObject subIndex="06" name="Consumer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101606" />
            <CANopenSubObject subIndex="07" name="Consumer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101607" />
            <CANopenSubObject subIndex="08" name="Consumer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101608" />
          </CANopenObject>
          <CANopenObject index="1017" name="Producer heartbeat time" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1017" />
          <CANopenObject index="1018" name="Identity" objectType="9" uniqueIDRef="UID_OBJ_1018" subNumber="5">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101800" />
            <CANopenSubObject subIndex="01" name="Vendor-ID" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101801" />
            <CANopenSubObject subIndex="02" name="Product code" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101802" />
            <CANopenSubObject subIndex="03" name="Revision number" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101803" />
            <CANopenSubObject subIndex="04" name="Serial number" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_101804" />
          </CANopenObject>
          <CANopenObject index="1019" name="Synchronous counter overflow value" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1019" />
          <CANopenObject index="1020" name="Verify configuration" objectType="8" uniqueIDRef="UID_OBJ_1020" subNumber="3">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102000" />
            <CANopenSubObject subIndex="01" name="Configuration date" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102001" />
            <CANopenSubObject subIndex="02" name="Configuration time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102002" />
          </CANopenObject>
          <CANopenObject index="1021" name="Store EDS" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1021" />
          <CANopenObject index="1022" name="Store format" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1022" />
          <CANopenObject index="1023" name="OS command" objectType="9" uniqueIDRef="UID_OBJ_1023" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102300" />
            <CANopenSubObject subIndex="01" name="Command" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102301" />
            <CANopenSubObject subIndex="02" name="Status" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102302" />
            <CANopenSubObject subIndex="03" name="Reply" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102303" />
          </CANopenObject>
          <CANopenObject index="1024" name="OS command mode" objectType="7" PDOmapping="no" uniqueIDRef="UID_OBJ_1024" />
          <CANopenObject index="1025" name="OS debugger interface" objectType="9" uniqueIDRef="UID_OBJ_1025" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102500" />
            <CANopenSubObject subIndex="01" name="Command" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102501" />
            <CANopenSubObject subIndex="02" name="Status" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102502" />
            <CANopenSubObject subIndex="03" name="Reply" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102503" />
          </CANopenObject>
          <CANopenObject index="1026" name="OS prompt" objectType="8" uniqueIDRef="UID_OBJ_1026" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102600" />
            <CANopenSubObject subIndex="01" name="StdIn" objectType="7" PDOmapping="optional" uniqueIDRef="UID_SUB_102601" />
            <CANopenSubObject subIndex="02" name="StdOut" objectType="7" PDOmapping="optional" uniqueIDRef="UID_SUB_102602" />
            <CANopenSubObject subIndex="03" name="StdErr" objectType="7" PDOmapping="optional" uniqueIDRef="UID_SUB_102603" />
          </CANopenObject>
          <CANopenObject index="1027" name="Module list" objectType="8" uniqueIDRef="UID_OBJ_1027" subNumber="2">
            <CANopenSubObject subIndex="00" name="Number of connected modules" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102700" />
            <CANopenSubObject subIndex="01" name="Module 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102701" />
          </CANopenObject>
          <CANopenObject index="1028" name="Emergency consumer object" objectType="8" uniqueIDRef="UID_OBJ_1028" subNumber="9">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102800" />
            <CANopenSubObject subIndex="01" name="Emergency consumer 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102801" />
            <CANopenSubObject subIndex="02" name="Emergency consumer 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102802" />
            <CANopenSubObject subIndex="03" name="Emergency consumer 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102803" />
            <CANopenSubObject subIndex="04" name="Emergency consumer 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102804" />
            <CANopenSubObject subIndex="05" name="Emergency consumer 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102805" />
            <CANopenSubObject subIndex="06" name="Emergency consumer 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102806" />
            <CANopenSubObject subIndex="07" name="Emergency consumer 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102807" />
            <CANopenSubObject subIndex="08" name="Emergency consumer 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102808" />
          </CANopenObject>
          <CANopenObject index="1029" name="Error behavior object" objectType="8" uniqueIDRef="UID_OBJ_1029" subNumber="3">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102900" />
            <CANopenSubObject subIndex="01" name="Communication error" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102901" />
            <CANopenSubObject subIndex="02" name="Profile- or manufacturer-specific error" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_102902" />
          </CANopenObject>
          <CANopenObject index="1200" name="SDO server parameter" objectType="9" uniqueIDRef="UID_OBJ_1200" subNumber="3">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_120000" />
            <CANopenSubObject subIndex="01" name="COB-ID client to server (rx)" objectType="7" PDOmapping="TPDO" uniqueIDRef="UID_SUB_120001" />
            <CANopenSubObject subIndex="02" name="COB-ID server to client (tx)" objectType="7" PDOmapping="TPDO" uniqueIDRef="UID_SUB_120002" />
          </CANopenObject>
          <CANopenObject index="1201" name="SDO server parameter" objectType="9" uniqueIDRef="UID_OBJ_1201" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_120100" />
            <CANopenSubObject subIndex="01" name="COB-ID client to server (rx)" objectType="7" PDOmapping="optional" uniqueIDRef="UID_SUB_120101" />
            <CANopenSubObject subIndex="02" name="COB-ID server to client (tx)" objectType="7" PDOmapping="optional" uniqueIDRef="UID_SUB_120102" />
            <CANopenSubObject subIndex="03" name="Node-ID of the SDO client" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_120103" />
          </CANopenObject>
          <CANopenObject index="1280" name="SDO client parameter" objectType="9" uniqueIDRef="UID_OBJ_1280" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_128000" />
            <CANopenSubObject subIndex="01" name="COB-ID client to server (tx)" objectType="7" PDOmapping="optional" uniqueIDRef="UID_SUB_128001" />
            <CANopenSubObject subIndex="02" name="COB-ID server to client (rx)" objectType="7" PDOmapping="optional" uniqueIDRef="UID_SUB_128002" />
            <CANopenSubObject subIndex="03" name="Node-ID of the SDO server" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_128003" />
          </CANopenObject>
          <CANopenObject index="1400" name="RPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1400" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140000" />
            <CANopenSubObject subIndex="01" name="COB-ID used by RPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140001" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140002" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140005" />
          </CANopenObject>
          <CANopenObject index="1401" name="RPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1401" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140100" />
            <CANopenSubObject subIndex="01" name="COB-ID used by RPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140101" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140102" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140105" />
          </CANopenObject>
          <CANopenObject index="1402" name="RPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1402" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140200" />
            <CANopenSubObject subIndex="01" name="COB-ID used by RPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140201" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140202" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140205" />
          </CANopenObject>
          <CANopenObject index="1403" name="RPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1403" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140300" />
            <CANopenSubObject subIndex="01" name="COB-ID used by RPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140301" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140302" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140305" />
          </CANopenObject>
          <CANopenObject index="1404" name="RPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1404" subNumber="4">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140400" />
            <CANopenSubObject subIndex="01" name="COB-ID used by RPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140401" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140402" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_140405" />
          </CANopenObject>
          <CANopenObject index="1600" name="RPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1600" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160000" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160001" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160002" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160003" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160004" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160005" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160006" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160007" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160008" />
          </CANopenObject>
          <CANopenObject index="1601" name="RPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1601" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160100" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160101" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160102" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160103" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160104" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160105" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160106" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160107" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160108" />
          </CANopenObject>
          <CANopenObject index="1602" name="RPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1602" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160200" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160201" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160202" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160203" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160204" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160205" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160206" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160207" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160208" />
          </CANopenObject>
          <CANopenObject index="1603" name="RPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1603" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160300" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160301" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160302" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160303" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160304" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160305" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160306" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160307" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160308" />
          </CANopenObject>
          <CANopenObject index="1604" name="RPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1604" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160400" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160401" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160402" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160403" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160404" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160405" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160406" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160407" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_160408" />
          </CANopenObject>
          <CANopenObject index="1800" name="TPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1800" subNumber="6">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180000" />
            <CANopenSubObject subIndex="01" name="COB-ID used by TPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180001" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180002" />
            <CANopenSubObject subIndex="03" name="Inhibit time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180003" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180005" />
            <CANopenSubObject subIndex="06" name="SYNC start value" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180006" />
          </CANopenObject>
          <CANopenObject index="1801" name="TPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1801" subNumber="6">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180100" />
            <CANopenSubObject subIndex="01" name="COB-ID used by TPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180101" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180102" />
            <CANopenSubObject subIndex="03" name="Inhibit time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180103" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180105" />
            <CANopenSubObject subIndex="06" name="SYNC start value" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180106" />
          </CANopenObject>
          <CANopenObject index="1802" name="TPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1802" subNumber="6">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180200" />
            <CANopenSubObject subIndex="01" name="COB-ID used by TPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180201" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180202" />
            <CANopenSubObject subIndex="03" name="Inhibit time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180203" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180205" />
            <CANopenSubObject subIndex="06" name="SYNC start value" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180206" />
          </CANopenObject>
          <CANopenObject index="1803" name="TPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1803" subNumber="6">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180300" />
            <CANopenSubObject subIndex="01" name="COB-ID used by TPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180301" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180302" />
            <CANopenSubObject subIndex="03" name="Inhibit time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180303" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180305" />
            <CANopenSubObject subIndex="06" name="SYNC start value" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180306" />
          </CANopenObject>
          <CANopenObject index="1804" name="TPDO communication parameter" objectType="9" uniqueIDRef="UID_OBJ_1804" subNumber="6">
            <CANopenSubObject subIndex="00" name="Highest sub-index supported" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180400" />
            <CANopenSubObject subIndex="01" name="COB-ID used by TPDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180401" />
            <CANopenSubObject subIndex="02" name="Transmission type" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180402" />
            <CANopenSubObject subIndex="03" name="Inhibit time" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180403" />
            <CANopenSubObject subIndex="05" name="Event timer" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180405" />
            <CANopenSubObject subIndex="06" name="SYNC start value" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_180406" />
          </CANopenObject>
          <CANopenObject index="1A00" name="TPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1A00" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0000" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0001" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0002" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0003" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0004" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0005" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0006" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0007" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0008" />
          </CANopenObject>
          <CANopenObject index="1A01" name="TPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1A01" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0100" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0101" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0102" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0103" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0104" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0105" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0106" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0107" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0108" />
          </CANopenObject>
          <CANopenObject index="1A02" name="TPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1A02" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0200" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0201" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0202" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0203" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0204" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0205" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0206" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0207" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0208" />
          </CANopenObject>
          <CANopenObject index="1A03" name="TPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1A03" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0300" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0301" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0302" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0303" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0304" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0305" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0306" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0307" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0308" />
          </CANopenObject>
          <CANopenObject index="1A04" name="TPDO mapping parameter" objectType="9" uniqueIDRef="UID_OBJ_1A04" subNumber="9">
            <CANopenSubObject subIndex="00" name="Number of mapped application objects in PDO" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0400" />
            <CANopenSubObject subIndex="01" name="Application object 1" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0401" />
            <CANopenSubObject subIndex="02" name="Application object 2" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0402" />
            <CANopenSubObject subIndex="03" name="Application object 3" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0403" />
            <CANopenSubObject subIndex="04" name="Application object 4" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0404" />
            <CANopenSubObject subIndex="05" name="Application object 5" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0405" />
            <CANopenSubObject subIndex="06" name="Application object 6" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0406" />
            <CANopenSubObject subIndex="07" name="Application object 7" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0407" />
            <CANopenSubObject subIndex="08" name="Application object 8" objectType="7" PDOmapping="no" uniqueIDRef="UID_SUB_1A0408" />
          </CANopenObject>
        </q2:CANopenObjectList>
        <dummyUsage>
          <dummy entry="Dummy0001=0" />
          <dummy entry="Dummy0002=1" />
          <dummy entry="Dummy0003=1" />
          <dummy entry="Dummy0004=1" />
          <dummy entry="Dummy0005=1" />
          <dummy entry="Dummy0006=1" />
          <dummy entry="Dummy0007=1" />
        </dummyUsage>
      </ApplicationLayers>
      <TransportLayers>
        <PhysicalLayer>
          <baudRate>
            <supportedBaudRate value="10 Kbps" />
            <supportedBaudRate value="20 Kbps" />
            <supportedBaudRate value="50 Kbps" />
            <supportedBaudRate value="125 Kbps" />
            <supportedBaudRate value="250 Kbps" />
            <supportedBaudRate value="500 Kbps" />
            <supportedBaudRate value="800 Kbps" />
            <supportedBaudRate value="1000 Kbps" />
          </baudRate>
        </PhysicalLayer>
      </TransportLayers>
      <NetworkManagement>
        <CANopenGeneralFeatures granularity="8" nrOfRxPDO="4" nrOfTxPDO="4" layerSettingServiceSlave="true" />
        <CANopenMasterFeatures />
      </NetworkManagement>
    </ProfileBody>
  </ISO15745Profile>
</ISO15745ProfileContainer>