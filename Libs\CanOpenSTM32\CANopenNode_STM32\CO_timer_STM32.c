/*
 * CANopen Dynamic Timer Management for STM32 Implementation
 * 
 * @file        CO_timer_STM32.c
 * <AUTHOR> Assistant
 * @date        2024
 * 
 * This file is part of CANopenNode, an opensource CANopen Stack.
 */

#include "CO_timer_STM32.h"
#include "stm32f1xx.h"

/* Forward declaration */
typedef struct CANopenNodeSTM32 CANopenNodeSTM32;

/* Static variables for dynamic timer management */
static uint32_t lastTime_us = 0;
static uint32_t timerNext_us = 1000; // Default 1ms
static bool_t timerInitialized = false;
static uint32_t lastTimeDifference_us = 0;

/* Function to get current time in microseconds */
uint32_t getCurrentTime_us(void)
{
    /* Use DWT (Data Watchpoint and Trace) cycle counter for high precision */
    /* This provides microsecond precision on Cortex-M3/M4 */
    static uint32_t initialized = 0;
    
    if (!initialized) {
        /* Enable DWT */
        CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
        DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;
        DWT->CYCCNT = 0;
        initialized = 1;
    }
    
    /* Get system clock frequency */
    uint32_t SystemCoreClock_MHz = SystemCoreClock / 1000000;
    
    /* Convert CPU cycles to microseconds */
    return DWT->CYCCNT / SystemCoreClock_MHz;
}

/* Function to set timer period dynamically with automatic prescaler calculation */
void setTimerPeriod_us(uint32_t period_us)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL) {
        TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

        /* Stop timer */
        HAL_TIM_Base_Stop_IT(htim);

        /* Get timer clock frequency with correct APB prescaler handling */
        uint32_t timer_clock_hz;

        if (htim->Instance == TIM1) {
            /* TIM1 is on APB2 */
            timer_clock_hz = HAL_RCC_GetPCLK2Freq();
            /* If APB2 prescaler != 1, timer clock is doubled */
            uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
            if (apb2_prescaler != 0) {  /* 0 means no division (prescaler = 1) */
                timer_clock_hz *= 2;
            }
        } else {
            /* Other timers are on APB1 */
            timer_clock_hz = HAL_RCC_GetPCLK1Freq();
            /* If APB1 prescaler != 1, timer clock is doubled */
            uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
            if (apb1_prescaler != 0) {  /* 0 means no division (prescaler = 1) */
                timer_clock_hz *= 2;
            }
        }

        /* Validate input period */
        if (period_us == 0) {
            period_us = 1; /* Minimum 1 microsecond */
        }

        uint32_t prescaler;
        uint32_t period_ticks;

        /* Fast path for small periods using 32-bit arithmetic */
        uint32_t max_32bit_period = UINT32_MAX / timer_clock_hz;  /* ~59ms at 72MHz */

        if (period_us <= max_32bit_period) {
            /* Fast 32-bit calculation for periods up to ~59ms */
            uint32_t total_ticks = (timer_clock_hz / 1000) * (period_us / 1000);

            /* Handle remainder for better precision */
            uint32_t remainder_hz = timer_clock_hz % 1000;
            uint32_t remainder_us = period_us % 1000;
            total_ticks += (remainder_hz * remainder_us) / 1000;

            if (total_ticks <= 65535) {
                /* Small period - use prescaler = 1 for best resolution */
                prescaler = 1;
                period_ticks = total_ticks;
            } else {
                /* Medium period - calculate optimal prescaler */
                prescaler = (total_ticks + 65534) / 65535; /* Round up division */
                period_ticks = total_ticks / prescaler;
            }
        } else {
            /* Slow path for large periods using 64-bit arithmetic */
            uint64_t total_ticks_64 = ((uint64_t)timer_clock_hz * (uint64_t)period_us) / 1000000ULL;

            /* Check if period is too large */
            uint64_t max_possible_ticks = (uint64_t)65535 * 65535; /* Max prescaler * Max ARR */
            if (total_ticks_64 > max_possible_ticks) {
                /* Period too large - clamp to maximum */
                total_ticks_64 = max_possible_ticks;
            }

            /* Large period - calculate minimum required prescaler */
            prescaler = (uint32_t)((total_ticks_64 + 65534) / 65535); /* Round up division */

            /* Recalculate period_ticks with exact prescaler */
            period_ticks = (uint32_t)(total_ticks_64 / prescaler);
        }

        /* Final validation */
        if (prescaler < 1) prescaler = 1;
        if (prescaler > 65535) prescaler = 65535;
        if (period_ticks < 1) period_ticks = 1;
        if (period_ticks > 65535) period_ticks = 65535;

        /* Set new prescaler and period (registers expect 0-based values) */
        __HAL_TIM_SET_PRESCALER(htim, prescaler - 1);
        __HAL_TIM_SET_AUTORELOAD(htim, period_ticks - 1);
        __HAL_TIM_SET_COUNTER(htim, 0);

        /* Generate update event to load new prescaler and ARR values immediately */
        htim->Instance->EGR = TIM_EGR_UG;

        /* Clear update interrupt flag that was set by UG */
        __HAL_TIM_CLEAR_FLAG(htim, TIM_FLAG_UPDATE);

        /* Restart timer */
        HAL_TIM_Base_Start_IT(htim);
    }
}

/* Initialize timer management */
void CO_timer_init_STM32(void)
{
    /* Initialize DWT by calling getCurrentTime_us once */
    getCurrentTime_us();

    /* Reset timer variables */
    lastTime_us = 0;
    timerNext_us = 1000;
    timerInitialized = false;
    lastTimeDifference_us = 0;
}

/* Process CANopen interrupt with dynamic timing */
void CO_timer_processInterrupt(bool_t *syncWas, uint32_t *timeDifference_us, uint32_t *timerNext_us_out)
{
    if (syncWas == NULL || timeDifference_us == NULL || timerNext_us_out == NULL) {
        return;
    }

    *syncWas = false;
    uint32_t currentTime_us = getCurrentTime_us();

    /* Initialize timer on first call */
    if (!timerInitialized) {
        lastTime_us = currentTime_us;
        timerInitialized = true;
        *timeDifference_us = 1000; // Default 1ms for first call
    } else {
        /* Calculate real time difference since last function call */
        *timeDifference_us = currentTime_us - lastTime_us;
        /* Prevent overflow and unreasonable values */
        if (*timeDifference_us > 100000) *timeDifference_us = 100000; // Max 100ms
        if (*timeDifference_us < 100) *timeDifference_us = 100;       // Min 100us
    }

    /* Initialize timerNext_us with maximum value */
    timerNext_us = UINT32_MAX;

    /* Update last time and statistics */
    lastTime_us = currentTime_us;
    lastTimeDifference_us = *timeDifference_us;

    /* Return timerNext_us for CANopen functions to modify */
    *timerNext_us_out = timerNext_us;
}

/* Get the next scheduled timer event in microseconds */
uint32_t CO_timer_getNextTimerEvent_us(void)
{
    return timerNext_us;
}

/* Process timer next event after CANopen functions */
void CO_timer_processNextEvent(uint32_t timerNext_us_from_canopen)
{
    /* Set timer for next interrupt based on CANopen scheduling */
    if (timerNext_us_from_canopen == UINT32_MAX || timerNext_us_from_canopen == 0) {
        timerNext_us = 1000; // Default 1ms if no specific timing required
    } else {
        timerNext_us = timerNext_us_from_canopen;
    }

    /* Apply new timer period */
    setTimerPeriod_us(timerNext_us);
}

/* Get timer period range and resolution */
void CO_timer_getRange(CO_timer_range_t *range)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (range != NULL) {
        if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL) {
            TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

            /* Get timer clock frequency with correct APB prescaler handling */
            uint32_t timer_clock_hz;

            if (htim->Instance == TIM1) {
                /* TIM1 is on APB2 */
                timer_clock_hz = HAL_RCC_GetPCLK2Freq();
                /* If APB2 prescaler != 1, timer clock is doubled */
                uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
                if (apb2_prescaler != 0) {
                    timer_clock_hz *= 2;
                }
            } else {
                /* Other timers are on APB1 */
                timer_clock_hz = HAL_RCC_GetPCLK1Freq();
                /* If APB1 prescaler != 1, timer clock is doubled */
                uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
                if (apb1_prescaler != 0) {
                    timer_clock_hz *= 2;
                }
            }

            /* Calculate range */
            range->timer_clock_hz = timer_clock_hz;
            range->min_period_us = (1000000 + timer_clock_hz - 1) / timer_clock_hz;  // 1 tick at prescaler=1, rounded up
            range->max_period_us = (uint32_t)(((uint64_t)65535 * 65535 * 1000000) / timer_clock_hz); // Max prescaler * Max ARR
            range->resolution_us = range->min_period_us;  // Best resolution with prescaler=1
        } else {
            /* Default values if timer not available */
            range->timer_clock_hz = 72000000;
            range->min_period_us = 1;
            range->max_period_us = 60000000; // ~60 seconds
            range->resolution_us = 1;
        }
    }
}

/* Get timing statistics for debugging */
void CO_timer_getTimingStats(CO_TimingStats_t *stats)
{
    if (stats != NULL) {
        stats->lastTimeDifference_us = lastTimeDifference_us;
        stats->nextTimerEvent_us = timerNext_us;
        stats->currentTime_us = getCurrentTime_us();
        stats->timerInitialized = timerInitialized;
    }
}

/* Get current timer configuration for debugging */
void CO_timer_getDebugInfo(uint32_t requested_period_us, CO_timer_debug_t *debug)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (debug != NULL) {
        debug->requested_period_us = requested_period_us;

        if (canopenNodeSTM32 != NULL && canopenNodeSTM32->timerHandle != NULL) {
            TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

            /* Get timer clock frequency */
            uint32_t timer_clock_hz;
            if (htim->Instance == TIM1) {
                timer_clock_hz = HAL_RCC_GetPCLK2Freq();
                uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
                if (apb2_prescaler != 0) {
                    timer_clock_hz *= 2;
                }
            } else {
                timer_clock_hz = HAL_RCC_GetPCLK1Freq();
                uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
                if (apb1_prescaler != 0) {
                    timer_clock_hz *= 2;
                }
            }

            /* Get current timer settings */
            debug->prescaler = __HAL_TIM_GET_PRESCALER(htim) + 1;
            debug->arr_value = __HAL_TIM_GET_AUTORELOAD(htim) + 1;
            debug->timer_clock_hz = timer_clock_hz;

            /* Calculate actual period */
            debug->actual_period_us = (debug->prescaler * debug->arr_value * 1000000) / timer_clock_hz;

            /* Calculate error percentage */
            if (requested_period_us > 0) {
                int32_t error = (int32_t)debug->actual_period_us - (int32_t)requested_period_us;
                debug->error_percent = (uint32_t)((error * 100) / (int32_t)requested_period_us);
                if (error < 0) debug->error_percent = (uint32_t)((-error * 100) / (int32_t)requested_period_us);
            } else {
                debug->error_percent = 0;
            }
        } else {
            /* Timer not available */
            debug->prescaler = 0;
            debug->arr_value = 0;
            debug->timer_clock_hz = 0;
            debug->actual_period_us = 0;
            debug->error_percent = 100;
        }
    }
}

/* Test function to demonstrate timer range capabilities */
void CO_timer_testRange(void)
{
    CO_timer_range_t range;
    CO_timer_getRange(&range);

    /* Test different periods */
    uint32_t test_periods[] = {
        100,        // 100 us
        1000,       // 1 ms
        10000,      // 10 ms
        100000,     // 100 ms
        1000000,    // 1 second
        5000000,    // 5 seconds
        10000000,   // 10 seconds
        30000000    // 30 seconds
    };

    for (int i = 0; i < 8; i++) {
        uint32_t period = test_periods[i];
        if (period >= range.min_period_us && period <= range.max_period_us) {
            setTimerPeriod_us(period);

            /* Get debug info */
            CO_timer_debug_t debug;
            CO_timer_getDebugInfo(period, &debug);

            /* Small delay to see the effect */
            for (volatile int j = 0; j < 1000000; j++);
        }
    }

    /* Return to default 1ms */
    setTimerPeriod_us(1000);
}

/* Run performance benchmark */
void CO_timer_benchmark(CO_timer_benchmark_t *result)
{
    extern CANopenNodeSTM32* canopenNodeSTM32;

    if (result == NULL || canopenNodeSTM32 == NULL || canopenNodeSTM32->timerHandle == NULL) {
        return;
    }

    TIM_HandleTypeDef *htim = canopenNodeSTM32->timerHandle;

    /* Get timer clock frequency */
    uint32_t timer_clock_hz;
    if (htim->Instance == TIM1) {
        timer_clock_hz = HAL_RCC_GetPCLK2Freq();
        uint32_t apb2_prescaler = (RCC->CFGR & RCC_CFGR_PPRE2) >> RCC_CFGR_PPRE2_Pos;
        if (apb2_prescaler != 0) {
            timer_clock_hz *= 2;
        }
    } else {
        timer_clock_hz = HAL_RCC_GetPCLK1Freq();
        uint32_t apb1_prescaler = (RCC->CFGR & RCC_CFGR_PPRE1) >> RCC_CFGR_PPRE1_Pos;
        if (apb1_prescaler != 0) {
            timer_clock_hz *= 2;
        }
    }

    /* Calculate threshold */
    result->threshold_us = UINT32_MAX / timer_clock_hz;

    /* Enable DWT for cycle counting */
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;

    /* Test fast path (32-bit) */
    uint32_t start_cycles = DWT->CYCCNT;

    /* Simulate fast path calculation */
    uint32_t period_us = 10000; // 10ms - should use fast path
    uint32_t total_ticks = (timer_clock_hz / 1000) * (period_us / 1000);
    uint32_t remainder_hz = timer_clock_hz % 1000;
    uint32_t remainder_us = period_us % 1000;
    total_ticks += (remainder_hz * remainder_us) / 1000;

    uint32_t prescaler, period_ticks;
    if (total_ticks <= 65535) {
        prescaler = 1;
        period_ticks = total_ticks;
    } else {
        prescaler = (total_ticks + 65534) / 65535;
        period_ticks = total_ticks / prescaler;
    }

    uint32_t end_cycles = DWT->CYCCNT;
    result->fast_path_cycles = end_cycles - start_cycles;

    /* Test slow path (64-bit) */
    start_cycles = DWT->CYCCNT;

    /* Simulate slow path calculation */
    period_us = 30000000; // 30 seconds - should use slow path
    uint64_t total_ticks_64 = ((uint64_t)timer_clock_hz * (uint64_t)period_us) / 1000000ULL;

    prescaler = (uint32_t)((total_ticks_64 + 65534) / 65535);
    period_ticks = (uint32_t)(total_ticks_64 / prescaler);

    end_cycles = DWT->CYCCNT;
    result->slow_path_cycles = end_cycles - start_cycles;

    /* Initialize counters */
    result->fast_path_count = 0;
    result->slow_path_count = 0;
}
